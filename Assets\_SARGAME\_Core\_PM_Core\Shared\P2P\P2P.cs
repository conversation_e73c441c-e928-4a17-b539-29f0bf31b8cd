//TABULA_GUID:{4FF5E913-24D7-4BF7-9460-61BFC4A6057E}
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using NobleConnect;
using NobleConnect.Ice;
using NobleConnectLib;
using NobleConnectLib.MatchUpDotNet;
using Tabula.Log;

// General P2P Class

namespace Tabula.P2P
{
	public class P2P : IDisposable
	{
		private static readonly string NC_GameId = "****************************************************************************************************************************************************";
		private static readonly string NC_MatchName = "S_ARGAME";

		// Thread-safe instance field access
		private readonly object _instanceLock = new object();
		private NobleConnectClient? _client;
		private NobleConnectServer? _server;
		private ClientData? _currentConnection;

		// Task management
		private readonly ConcurrentDictionary<int, Task> _activeTasks = new ConcurrentDictionary<int, Task>();
		private readonly CancellationTokenSource _globalCancellation = new CancellationTokenSource();
		private int _taskIdCounter = 0;
		private bool _disposed = false;

		// Configuration properties
		public int UpdateTimerRate { get; set; }
		public bool ForceRelay { get; set; }
		public int ServerMaxClients { get; set; }

		// Constructor
		public P2P(int updateTimerRate = 100, bool forceRelay = false, int serverMaxClients = 10000)
		{
			UpdateTimerRate = updateTimerRate;
			ForceRelay = forceRelay;
			ServerMaxClients = serverMaxClients;
		}

		// Thread-safe properties
		private NobleConnectClient? client
		{
			get { lock (_instanceLock) { return _client; } }
			set { lock (_instanceLock) { _client = value; } }
		}

		private NobleConnectServer? server
		{
			get { lock (_instanceLock) { return _server; } }
			set { lock (_instanceLock) { _server = value; } }
		}


		#region Server

		private Matchmaker? _hostMatchmakerInstance;
		private CancellationTokenSource? _server_tokensrc;

		// Thread-safe server properties
		private Matchmaker? hostMatchmakerInstance
		{
			get { lock (_instanceLock) { return _hostMatchmakerInstance; } }
			set { lock (_instanceLock) { _hostMatchmakerInstance = value; } }
		}

		// Task management helper methods
		private int RegisterTask(Task task)
		{
			int taskId = Interlocked.Increment(ref _taskIdCounter);
			_activeTasks.TryAdd(taskId, task);

			// Clean up completed task
			task.ContinueWith(t =>
			{
				_activeTasks.TryRemove(taskId, out _);
				if (t.IsFaulted && t.Exception != null)
				{
					Log.Logger.DefaultLog.WriteLine($"[P2P] Background task {taskId} failed: {t.Exception.GetBaseException().Message}");
				}
			}, TaskContinuationOptions.ExecuteSynchronously);

			return taskId;
		}

		private void CancelAllTasks()
		{
			try
			{
				_globalCancellation.Cancel();

				var tasks = _activeTasks.Values.ToArray();
				if (tasks.Length > 0)
				{
					Log.Logger.DefaultLog.WriteLine($"[P2P] Waiting for {tasks.Length} background tasks to complete...");
					Task.WaitAll(tasks, TimeSpan.FromSeconds(5));
				}
			}
			catch (Exception ex)
			{
				Log.Logger.DefaultLog.WriteLine($"[P2P] Error during task cancellation: {ex.Message}");
			}
		}
		public class ServerData
		{
			public enum State
			{
				None = 0,
				ConnectedAndPublished = 1,

				ServerFatalError = -1,
				ServerOfferFailed = -2,
				MatchUpLostConnection = -3,
				MatchUpCreationFailed = -4


			}

			public State	status;

			public string	ip;			// public ip
			public int		port;		// public port
			public Match	match;      // match data

			public string	join_code;
		}

		public void StartAndPublishServer(int local_server_port, string join_code, string server_type, Action<ServerData> OnResult=null)
		{
			var serverTask = Task.Run(() => RunServer(
				gameId: NC_GameId,
				forceRelay: ForceRelay,
				logLevel: NobleConnect.Logger.Level.Info,
				updateTimerRateMs: UpdateTimerRate,
				localServerPort: local_server_port,
				OnServerPrepared: (public_ip, public_port) =>
				{
						// Starting MatchUp
						// TODO: add server type
					var matchupTask = Task.Run(() => RunMatchUpHost(
						public_ip,
						(int) public_port,
						NC_MatchName,
						join_code,
						OnMatchResult: (success, match) =>
						{
							lock (_instanceLock)
							{
								if (success)
								{
									OnResult?.Invoke(new ServerData()
									{
										status = ServerData.State.ConnectedAndPublished,
										ip = public_ip,
										port = public_port,
										match = match,
										join_code = join_code
									});
								}
								else
								{
									OnResult?.Invoke(new ServerData()
									{
										status = ServerData.State.MatchUpCreationFailed,
										ip = null,
										port = -1,
										match = match
									});
								}
							}
						},
						OnLostConnection: (ex) =>
						{
							lock (_instanceLock)
							{
								OnResult?.Invoke(new ServerData()
								{
									status = ServerData.State.MatchUpLostConnection,
									ip = null,
									port = -1,
									match = null
								});
							}
						}));
					RegisterTask(matchupTask);
				},
				OnFatalError: (s) =>
					{
						lock (_instanceLock)
						{
							OnResult?.Invoke(new ServerData()
							{
								status = ServerData.State.ServerFatalError,
								ip = null,
								port = -1,
								match = null
							});
						}
					},
				OnOfferFailed: () =>
					{
						lock (_instanceLock)
						{
							OnResult?.Invoke(new ServerData()
							{
								status = ServerData.State.ServerOfferFailed,
								ip = null,
								port = -1,
								match = null
							});
						}
					}
				));
			RegisterTask(serverTask);
		}

		private async Task RunServer(string gameId,
			bool forceRelay,
			NobleConnect.Logger.Level logLevel,
			int updateTimerRateMs,
			int localServerPort,
			Action<string, ushort> OnServerPrepared,
			Action<string> OnFatalError,
			Action OnOfferFailed
			)
		{
#if DEBUG
			Log.Logger.DefaultLog.WriteLine($"[SANDBOX] Using NobleConnect account: {NobleConnectHelper.ParseGameID(gameId).username}");
			Log.Logger.DefaultLog.WriteLine($"[SANDBOX] Using NobleConnect gameId: {gameId}");
#endif

			CancellationTokenSource? localTokenSource = null;
			try
			{
				lock (_instanceLock)
				{
					_server_tokensrc?.Cancel();
					_server_tokensrc?.Dispose();
					_server_tokensrc = new CancellationTokenSource();
					localTokenSource = _server_tokensrc;
				}

				var iceConfig = new IceConfig
				{
					iceServerAddress = RegionURL.FromRegion(GeographicRegion.AUTO),
					icePort = 3478,
					username = NobleConnectHelper.ParseGameID(gameId).username,
					password = NobleConnectHelper.ParseGameID(gameId).password,
					origin = NobleConnectHelper.ParseGameID(gameId).origin,
					useSimpleAddressGathering = true,
					onFatalError = (s) =>
						{
							try
							{
								Log.Logger.DefaultLog.WriteLine($"[OnFatalError] {s} - Ending session");
								OnFatalError?.Invoke(s);
								StopServer();
							}
							catch (Exception ex)
							{
								Log.Logger.DefaultLog.WriteLine($"[OnFatalError] Exception in callback: {ex.Message}");
							}
						},
					onOfferFailed = () =>
						{
							try
							{
								Log.Logger.DefaultLog.WriteLine("[OnOfferFailed] Initialize Peer FAILED - Ending session");
								OnOfferFailed?.Invoke();
								StopServer();
							}
							catch (Exception ex)
							{
								Log.Logger.DefaultLog.WriteLine($"[OnOfferFailed] Exception in callback: {ex.Message}");
							}
						},
					forceRelayOnly = forceRelay,
					enableIPv6 = false
				};

				server = new NobleConnectServer(localServerPort, iceConfig, logLevel, updateTimerRateMs);

				if (localTokenSource != null && !localTokenSource.Token.IsCancellationRequested)
				{
					server.Initialize(localTokenSource.Token, (ip, port) =>
					{
						try
						{
							Log.Logger.DefaultLog.WriteLine($"[SERVER] Peer: Ready To Host");
							Log.Logger.DefaultLog.WriteLine($"[SERVER] Bridge EndPoint: {ip}, port: {port}");
							OnServerPrepared?.Invoke(ip, port);
						}
						catch (Exception ex)
						{
							Log.Logger.DefaultLog.WriteLine($"[SERVER] Exception in OnServerPrepared callback: {ex.Message}");
						}
					});
				}
			}
			catch (Exception ex)
			{
				Log.Logger.DefaultLog.WriteLine($"[SERVER] Exception in RunServer: {ex.Message}");
				throw;
			}
		}

		/// <summary>
		/// Safely destroys bridges with retry logic to handle concurrent modification exceptions
		/// </summary>
		/// <param name="peer">The peer whose bridges should be destroyed</param>
		/// <param name="peerType">Type of peer for logging purposes</param>
		private void SafeDestroyBridges(Peer peer, string peerType)
		{
			if (peer == null) return;

			const int maxRetries = 3;
			const int baseDelayMs = 50;

			for (int attempt = 0; attempt < maxRetries; attempt++)
			{
				try
				{
					Log.Logger.DefaultLog.WriteLine($"[{peerType}] Attempting to destroy bridges (attempt {attempt + 1}/{maxRetries})");
					peer.DestroyBridges();
					Log.Logger.DefaultLog.WriteLine($"[{peerType}] Successfully destroyed bridges");
					return;
				}
				catch (InvalidOperationException ex) when (ex.Message.Contains("Collection was modified") || ex.Message.Contains("enumeration operation may not execute"))
				{
					Log.Logger.DefaultLog.WriteLine($"[{peerType}] Bridge destruction failed due to concurrent modification (attempt {attempt + 1}/{maxRetries}): {ex.Message}");

					if (attempt < maxRetries - 1)
					{
						// Exponential backoff with jitter
						int delay = baseDelayMs * (int)Math.Pow(2, attempt) + new Random().Next(0, 25);
						Thread.Sleep(delay);
					}
					else
					{
						Log.Logger.DefaultLog.WriteLine($"[{peerType}] Failed to destroy bridges after {maxRetries} attempts. Proceeding with cleanup anyway.");
					}
				}
				catch (ObjectDisposedException ex)
				{
					Log.Logger.DefaultLog.WriteLine($"[{peerType}] Peer already disposed during bridge destruction: {ex.Message}");
					return; // Already disposed, nothing to do
				}
				catch (NullReferenceException ex)
				{
					Log.Logger.DefaultLog.WriteLine($"[{peerType}] Null reference during bridge destruction (likely already cleaned up): {ex.Message}");
					return; // Already cleaned up
				}
				catch (Exception ex)
				{
					Log.Logger.DefaultLog.WriteLine($"[{peerType}] Unexpected error during bridge destruction: {ex.Message}");
					break; // Don't retry for unexpected exceptions
				}
			}
		}

		public void StopServer()
		{
			try
			{
				Log.Logger.DefaultLog.WriteLine("[SERVER] Stopping server...");

				// Thread-safe cancellation
				CancellationTokenSource? tokenSource = null;
				Matchmaker? matchmaker = null;
				NobleConnectServer? serverInstance = null;

				lock (_instanceLock)
				{
					tokenSource = _server_tokensrc;
					_server_tokensrc = null;
					matchmaker = _hostMatchmakerInstance;
					_hostMatchmakerInstance = null;
					serverInstance = _server;
					_server = null;
				}

				// Cancel server operations
				try
				{
					tokenSource?.Cancel();
				}
				catch (ObjectDisposedException)
				{
					// Token source already disposed, ignore
				}

				// Destroy match
				try
				{
					matchmaker?.DestroyMatch();
				}
				catch (Exception ex)
				{
					Log.Logger.DefaultLog.WriteLine($"[SERVER] Error destroying match: {ex.Message}");
				}

				// Clean up server
				if (serverInstance != null)
				{
					try
					{
						if (serverInstance.peer != null)
						{
							SafeDestroyBridges(serverInstance.peer, "SERVER");
							serverInstance.peer.CleanUpEverything();
						}
						serverInstance.SafeDispose();
					}
					catch (Exception ex)
					{
						Log.Logger.DefaultLog.WriteLine($"[SERVER] Error during server cleanup: {ex.Message}");
					}
				}

				// Dispose token source
				try
				{
					tokenSource?.Dispose();
				}
				catch (Exception ex)
				{
					Log.Logger.DefaultLog.WriteLine($"[SERVER] Error disposing token source: {ex.Message}");
				}

				Log.Logger.DefaultLog.WriteLine("[SERVER] Server stopped successfully");
			}
			catch (Exception ex)
			{
				Log.Logger.DefaultLog.WriteLine($"[SERVER] Error during server shutdown: {ex.Message}");
			}
		}

		public void RunMatchUpHost(
			string hostAddress,
			int hostPort,
			string matchName,
			string join_code,
			Action<bool, Match> OnMatchResult,
			Action<Exception> OnLostConnection)
		{
#if DEBUG
			Log.Logger.DefaultLog.WriteLine($"[SANDBOX] Start hosting a match..");
#endif

			hostMatchmakerInstance = new Matchmaker();
			hostMatchmakerInstance.onLostConnectionToMatchmakingServer += delegate (Exception e)
			{
				Log.Logger.DefaultLog.WriteLine($"[Matchmaker host] Lost Connection To Matchmaker Server: {e}");
				OnLostConnection?.Invoke(e);
			};

			// TODO:
			var matchData = new Dictionary<string, MatchData>() {
			{ "Match name", matchName },
			{ "Host Address", hostAddress },
			{ "Host Port", hostPort },
			{ "DeviceID", join_code}	// TODO
		};

			// Create the Match with the associated MatchData
			hostMatchmakerInstance.CreateMatch(ServerMaxClients, matchData, 
				(bool success, Match match) =>
				{
					if (success)
					{
						Log.Logger.DefaultLog.WriteLine($"[Matchmaker host] Match created with data:" +
										  $"\n- ID: {match.id.ToString()}" +
										  $"\n- Name: {match.matchData["Match name"]}" +
										  $"\n- Host Unique ID: {match.matchData["DeviceID"]}");

						OnMatchResult?.Invoke(true, match);
					}
					else
					{
						Log.Logger.DefaultLog.WriteLine($"[Matchmaker host] Match creation failed");

						OnMatchResult?.Invoke(false, match);
					}

				});
		}

		#endregion

		#region Client

		public class ClientData
		{
			public enum State
			{
				None = 0,
				Connected = 1,

				ClientFatalError = -1,
				ClientOfferFailed = -2,

				MatchUpLostConnection = -3,
				MatchUpCreationFailed = -4,

				// extra
				Timeout = -5
			}

			public State status;

			public string	ip;           // public ip
			public int		port;        // public port
			public ConnectionType	connection_type;
			public Match	match;     // match data
			public Matchmaker matchMakerInstance;
		}

		// Will keep track of current connection - thread-safe property
		public ClientData CurrentConnection
		{
			get { lock (_instanceLock) { return _currentConnection; } }
			private set { lock (_instanceLock) { _currentConnection = value; } }
		}

		public void StartClient(string join_code, Action<ClientData> OnResult=null)
		{
			Matchmaker clientMatchmakerInstance = new Matchmaker();
			clientMatchmakerInstance.onLostConnectionToMatchmakingServer += delegate (Exception e)
			{
				Log.Logger.DefaultLog.WriteLine($"[Matchmaker client] Lost Connection To Matchmaker Server: {e}");
			};

			// Save Game ID in PlayerPrefs
			//PlayerPrefs.SetString("lastUsedHostDeviceUniqueID", clientGameIDInputField.text);

			JoinMatchWithDeviceUniqueID(
				clientMatchmakerInstance,
				join_code,
				OnMatchJoined: (match) =>
				{
					if (match == null)
					{
						// no match found, return a null result
						OnResult?.Invoke(null);
						return;
					}

					Log.Logger.DefaultLog.WriteLine($"[Matchmaker client] Client has joined match with ID: {match.id}");

					var clientTask = Task.Run(() => RunClient(NC_GameId,
						forceRelay: ForceRelay,
						match.matchData["Host Address"],
						match.matchData["Host Port"],
						NobleConnect.Logger.Level.Info,
						(string ip, int port, ConnectionType connection_type) =>
							{
								try
								{
									Log.Logger.DefaultLog.WriteLine($"[Matchmaker client] Client connection succesful: Bridge EndPoint: {ip}, port: {port}");

									CurrentConnection = new ClientData()
									{
										status = ClientData.State.Connected,
										ip = ip,
										port = port,
										connection_type = connection_type,
										match = match,
										matchMakerInstance = clientMatchmakerInstance
									};

									OnResult?.Invoke(CurrentConnection);
								}
								catch (Exception ex)
								{
									Log.Logger.DefaultLog.WriteLine($"[CLIENT] Exception in OnReadyToConnect callback: {ex.Message}");
								}
							},
						OnFatalError: (s) =>
							{
								try
								{
									CurrentConnection = new ClientData()
									{
										status = ClientData.State.ClientFatalError,
										ip = null,
										port = -1,
										match = null,
										matchMakerInstance = null
									};

									OnResult?.Invoke(CurrentConnection);
								}
								catch (Exception ex)
								{
									Log.Logger.DefaultLog.WriteLine($"[CLIENT] Exception in OnFatalError callback: {ex.Message}");
								}
							},
						OnOfferFailed: () =>
						{
							try
							{
								CurrentConnection = new ClientData()
								{
									status = ClientData.State.ClientFatalError,
									ip = null,
									port = -1,
									match = null,
									matchMakerInstance = null
								};

								OnResult?.Invoke(CurrentConnection);
							}
							catch (Exception ex)
							{
								Log.Logger.DefaultLog.WriteLine($"[CLIENT] Exception in OnOfferFailed callback: {ex.Message}");
							}
						}));
					RegisterTask(clientTask);
				});
		}

		public void JoinMatchWithDeviceUniqueID(Matchmaker clientMatchmakerInstance, string join_code, Action<Match> OnMatchJoined)
		{
			var filters = new List<MatchFilter>()
			{
				new MatchFilter("DeviceID", join_code)
			};

			clientMatchmakerInstance.GetMatchList(delegate (bool success, Match[] matches)
			{
				if (success)
				{
					if (matches.Length > 0)
					{
						var lastMatch = matches.OrderByDescending(x => x.id).First();
						clientMatchmakerInstance.JoinMatch(lastMatch,
							(b, match) =>
							{
								Log.Logger.DefaultLog.WriteLine(
									$"[Matchmaker client] Successfully joined match with ID: {match.id.ToString()}");
								OnMatchJoined.Invoke(match);
							});
					}
					else
					{
						Log.Logger.DefaultLog.WriteLine($"[Matchmaker client] No match found!");
						OnMatchJoined?.Invoke(null);
					}
				}
				else
				{
					Log.Logger.DefaultLog.WriteLine($"[Matchmaker client] cannot retrieve current match list");
					OnMatchJoined?.Invoke(null);
				}
			}, 0, 100, filters);
		}

		public Task RunClient(
			string gameId,
			bool forceRelay,
			string ip,
			int port,
			NobleConnect.Logger.Level logLevel,
			Action<string, int, ConnectionType> OnReadyToConnect,
			Action<string> OnFatalError,
			Action OnOfferFailed)
		{
			return Task.Run(() =>
			{
				try
				{
#if DEBUG
					Log.Logger.DefaultLog.WriteLine($"[SANDBOX] Using NobleConnect account: {NobleConnectHelper.ParseGameID(gameId).username}");
					Log.Logger.DefaultLog.WriteLine($"[SANDBOX] Using NobleConnect gameId: {gameId}");
#endif

					var _iceConfig = new IceConfig
					{
						iceServerAddress = RegionURL.FromRegion(GeographicRegion.AUTO),
						icePort = 3478,
						username = NobleConnectHelper.ParseGameID(gameId).username,
						password = NobleConnectHelper.ParseGameID(gameId).password,
						origin = NobleConnectHelper.ParseGameID(gameId).origin,
						useSimpleAddressGathering = true,
						onFatalError = (s) =>
							{
								try
								{
									Log.Logger.DefaultLog.WriteLine($"[OnFatalError] {s} - Ending session");
									OnFatalError?.Invoke(s);
									StopClient();
								}
								catch (Exception ex)
								{
									Log.Logger.DefaultLog.WriteLine($"[OnFatalError] Exception in callback: {ex.Message}");
								}
							},
						onOfferFailed = () =>
							{
								try
								{
									Log.Logger.DefaultLog.WriteLine("[OnOfferFailed] Initialize Peer FAILED - Ending session");
									OnOfferFailed?.Invoke();
									StopClient();
								}
								catch (Exception ex)
								{
									Log.Logger.DefaultLog.WriteLine($"[OnOfferFailed] Exception in callback: {ex.Message}");
								}
							},
						forceRelayOnly = forceRelay,
						enableIPv6 = false
					};

					client = new NobleConnectClient(_iceConfig, logLevel);

					// Address and Port of the server
					IPEndPoint serverEndPoint = new IPEndPoint(IPAddress.Parse(ip), port);

					client.Initialize(serverEndPoint, endPoint =>
					{
						try
						{
							var currentClient = client; // Capture reference to avoid race condition
							if (currentClient?.peer != null)
							{
								Log.Logger.DefaultLog.WriteLine($"[CLIENT] Peer: Ready To Connect (connection type {currentClient.peer.latestConnectionType})" +
										  $" on Local EndPoint: {endPoint.Address.ToString()}, port: {endPoint.Port}");

								OnReadyToConnect.Invoke(endPoint.Address.ToString(), endPoint.Port, currentClient.peer.latestConnectionType);
							}
						}
						catch (Exception ex)
						{
							Log.Logger.DefaultLog.WriteLine($"[CLIENT] Exception in Initialize callback: {ex.Message}");
						}
					});
				}
				catch (Exception ex)
				{
					Log.Logger.DefaultLog.WriteLine($"[CLIENT] Exception in RunClient: {ex.Message}");
					throw;
				}
			});
		}

		public void StopClient(Matchmaker matchmakerInstance = null)
		{
			try
			{
				Log.Logger.DefaultLog.WriteLine("[CLIENT] Stopping client...");

				// Thread-safe client cleanup
				NobleConnectClient? clientInstance = null;

				lock (_instanceLock)
				{
					clientInstance = _client;
					_client = null;
					_currentConnection = null;
				}

				// Leave match
				try
				{
					matchmakerInstance?.LeaveMatch();
				}
				catch (Exception ex)
				{
					Log.Logger.DefaultLog.WriteLine($"[CLIENT] Error leaving match: {ex.Message}");
				}

				// Clean up client
				if (clientInstance != null)
				{
					try
					{
						if (clientInstance.peer != null)
						{
							SafeDestroyBridges(clientInstance.peer, "CLIENT");
							clientInstance.peer.CleanUpEverything();
						}
						clientInstance.SafeDispose();
					}
					catch (Exception ex)
					{
						Log.Logger.DefaultLog.WriteLine($"[CLIENT] Error during client cleanup: {ex.Message}");
					}
				}

				Log.Logger.DefaultLog.WriteLine("[CLIENT] Client stopped successfully");
			}
			catch (Exception ex)
			{
				Log.Logger.DefaultLog.WriteLine($"[CLIENT] Error during client shutdown: {ex.Message}");
			}
		}

		/// <summary>
		/// Cleanup all P2P resources and cancel all background tasks
		/// </summary>
		public void Cleanup()
		{
			try
			{
				Log.Logger.DefaultLog.WriteLine("[P2P] Starting global cleanup...");

				StopClient();
				StopServer();
				CancelAllTasks();

				Log.Logger.DefaultLog.WriteLine("[P2P] Global cleanup completed");
			}
			catch (Exception ex)
			{
				Log.Logger.DefaultLog.WriteLine($"[P2P] Error during global cleanup: {ex.Message}");
			}
		}

		#endregion

		#region IDisposable Implementation

		public void Dispose()
		{
			Dispose(true);
			GC.SuppressFinalize(this);
		}

		protected virtual void Dispose(bool disposing)
		{
			if (disposing && !_disposed)
			{
				Cleanup();
				_globalCancellation?.Dispose();
				_disposed = true;
			}
		}

		~P2P()
		{
			Dispose(false);
		}

		#endregion
	}
}