- This is the source repository of S-ARGAME SDK, which is a custom game framework whose source is Assets/_SARGAME/_Core and Assets/_SARGAME/_Ext folders.
- When reasoning on the framework code also consider the games that are made with it, the game files are in the external Assets/_SARGAME folder ands ubfolders, in particular:
    -  _SARGAME/WALLEROIDS
    -  _SARGAME/NEONRAIDERS
    -  _SARGAME/PONKANOID
    -  _SARGAME/TABLERACE
- never change the games code, as it is not inside this repo, this repo is only for the main SDK
- there are many other folders under Assets folder, but those are mainly unity plugin, do not consider them too much as I am more interested in the game code.

Coding Style
- avoid overcomplicated or over-engineered solutions, try not create any new classes unless necessary and ask me before doing so.
- make minimal changes to the SDK
- try to be concise, do not intruduce new classes and keep new method to a minimal (i prefer #region inside an existing method instead of a new method)
- the SDK folder _SARGAME\_Core\_PM_Core\generated contains the data model and the generated code, changing anything here forces me to update the whole communication protocol, so do not do it never, only if strictly necessary ask me first.

<PERSON><PERSON><PERSON> use sequential-thinking and task-manager.