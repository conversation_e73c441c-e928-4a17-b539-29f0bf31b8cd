using System.Collections;
using System.Collections.Generic;
using Tabula.SharedObjectMap;
using UnityEngine;
using Tabula.PMCore;
using Tabula.Log;
using System.IO;
using System.Threading.Tasks;
using Newtonsoft.Json;
using System;
using UnityEngine.Events;
#if USE_ALINE
using Drawing;
#endif
using System.Linq;
using TMPro;

using Tabula.Unity;
using Tabula.TouchMyBrand;
using Newtonsoft.Json.Serialization;

#if UNITY_EDITOR
using UnityEditor;
using Newtonsoft.Json.Linq;
#endif

using Tabula.PWG;
using Tabula.P2P;
using Tabula.Licensing.LicenseActivator;
using Tabula.Licensing;
using Tabula.PWG.SARGAME;
using System.Text;
using Sentry;
using UnityEngine.UIElements;
using static Tabula.PMCore.Unity.IPMWrapper;
using Tabula.RPC;
using System.Collections.Concurrent;
using UnityEngine.Video;
using UnityEngine.UI;
using Klak.Spout;
using UnityEngine.Rendering;


// Use the package from solodalloy.. brings some dependencies
// "com.solidalloy.type.references": "2.8.0",
#if USE_SOLIDALLOY_TYPEREFERENCES
using TypeReferences;
#endif

namespace Tabula.PMCore.Unity
{
    [Serializable]
    public class PMCoreModelEvent : UnityEvent<Tabula.PMCore.Model>
    { }

    [Serializable]
    public class PMCoreServerEvent : UnityEvent<Tabula.PMCore.Unity.PMCore_Server>
    { }

    [Serializable]
    public class GuidObjectEvent : UnityEvent<Tabula.SharedObjectMap.GuidObject, IPMWrapper>
    { }

    [Serializable]
    public class PlayerSessionEvent : UnityEvent<Tabula.PMCore.PlayerJoinResult>
    { }

    /*
    [Serializable]
    public class PMCoreApplicationStatusChangeEvent : UnityEvent<ApplicationStatus>
    { }
    */
    [Serializable]
    public class PMCoreApplicationStatusChangeEvent : UnityEvent<string>
    { }

	[DefaultExecutionOrder(-50)]
	public class PMCore_Server : MonoBehaviour
    {        
        public Model Model => Server.Model;
        public ModelWrapper Wrapper;

        [Header("Model")]
        public string EmptyModel = "project_empty.json";
        [Tooltip("Path to server-side serialized project to load at startup if requested, otherwise the client must provide it.")]
        public string SerializedModelPath = "";     // json file for the project
        public string ScenarioId = "";              // id of the the scenario
        public Model.Flags ProjectFlags;            // flags of the project

		public string ProjectFolder => Path.GetDirectoryName(Path.GetFullPath(SerializedModelPath));

        [Tooltip("Will load the server-side model at startup (in a Build), client need to retrieve it with ReceiveModel() after connection")]
        public bool LoadModelInBuild = true;

        [Tooltip("Will load the server-side model at startup (Editor only), client need to retrieve it with ReceiveModel() after connection")]
        public bool LoadModelInEditor = true;

        // Internal events, setup by Server_Local component
        public Action<Model>                        BeforeSetModel;                     // model has been loaded, not yet set, it can be modified at runtime
        public Action                               OnModelCreated;                     // model has been istantiated, entities are created
        public Action                               OnServerConfigure;
        public Action<SharedObjectMap.GuidObject, IPMWrapper>       
                                                    OnObjectAdded, OnObjectRemoved;     // events hooked to shared object addition/deletion
        public Func<string, PlayerJoinResult>       OnNetworkPlayerJoined;
		public Func<string, int, PlayerLeftResult>  OnNetworkPlayerLeft;                // TODO: on player keepalive failed
        public Action<string>                       OnApplicationStatusChanged;         // Changing from editing to playing etc..

        public Action<Shared.KcpMessage>            OnCustomMessageReceived;             // higher level intercept custom messages (not RPC) (Tabula.RPC.Server -> SharedObjectMap_Server -> here)

        public Func<List<NamedValue>>               OnGetStatistics;                    // GM will populate it or others


		// Native events, must be subscribed by code (useful in managers)
		public Action<string, object>               OnPropertySet;                      // set generic properties
        public Func<string, object>                 OnPropertyGet;                      // get generic properties
        public Func<string, object[], object>       OnMethodCall;                       // call generic method

        // Standardized entity creation
        public Func<ScriptableObject, IPMEntity>    OnEntityCreateFromConfig;           // if defined will inject the creation depending on other framework modules (es: PWGCore.GameEntity)

        public Func<List<Entity>>                   OnGetEntityTemplates;               // declarative templates

        [Space(10)]
        [Header("Networking")]
        public float                                NetworkPlayerTimeout = 30;           // seconds after which a network player i considered disconnected
        public bool                                 EnableNetworking = false;           // start networking p2p service, get auth codes
        public string                               AuthCode;                           // for playing
        public string                               AdminAuthCode;                      // for calibration

		// Network discovery
		[Flags]
		public enum NetworkDiscoveryMode
		{
			Off = 0,
			Local = 1 << 1,
			P2P = 1 << 2,
		}

		public NetworkDiscoveryMode                 DiscoveryMode = PMCore_Server.NetworkDiscoveryMode.Local;   // local network is default
		private NetworkDiscovery                    NetworkDiscoveryServer;
		private int                                 P2PServerPublishTries = 0;
		const int                                   P2PServerPublishMaxTries = 10;

		// P2P instance for this server
		private P2P                                 _p2pInstance;

		[Space(10)]
        [Header("Licensing")]
        public LocalApp_Scriptable                  LocalApp;       // contains product, package, module, license
        internal SARGAME                            SARGAME;

        [Space(10)]
        [Header("Sentry")]
        public string                               SentryTransaction;

		// Special debug flags
		public static bool DebugMode { get; private set; } = false;
		public static bool TestingMode { get; private set; } = false;

		[Space(10)]
		[Header("Prefabs")]
		// Prefab map type (GuidObject) -> IPMWrapper
		[Tooltip("Map dynamic GuidObject derived types to prefabs implementing IPMWrapper interface. Instances will be parented to transform container.")]
        public List<PrefabMapItem>      PrefabMap = new List<PrefabMapItem>();

        [Tooltip("Map entities with standard interface.")]
        public List<EntityPrefabMapItem> EntityPrefabMap = new List<EntityPrefabMapItem>();

		[Tooltip("Map of generic resources")]
		public List<ResourceList>       ResourceMap = new List<ResourceList>();

        private Dictionary<System.Type, PrefabMapItem> _prefabmap = new Dictionary<Type, PrefabMapItem>();                   // for speed
        private Dictionary<string, EntityPrefabMapItem> _entityprefabmap = new Dictionary<string, EntityPrefabMapItem>();    // for speed

        [Header("Screen")]
        [Tooltip("The Application camera, rendering everything but excluding PMCore layer")]
        public Camera                       Camera;                               // output camera
        public Camera                       InternalCamera;                       // the pmcore camera
        public Transform                    OutputSurfaceContainer;               // container of outputsurfaces
        public List<PMOutputSurfaceWrapper> OutputSurfaces;
        public RenderTexture                OutputRenderTexture;                  // the render texture for game rendering
        public Canvas                       Overlay;                              // Overlay for UI and messages
		
        public float                        CameraSizeOriginal { get; private set; }  = 1f;   // the original orthographic scale
		public float                        CameraScale = 1f;
		private int                         layer_pmcore = -1;

		[Header("Background video")]
        public Canvas                       BackgroundCanvas;  
        public VideoPlayer                  BackgroundVideoPlayer;
        public RenderTexture                BackgroundVideoTexture;
        public RawImage                     BackgroundVideoImage;

		[Header("Special")]
		public bool                         DisablePM = false;                    // disables projection mapping, output surfaes and warping
        public bool                         DisableServer = false;                // disables the RPC server

		[Header("Spout")]
		public int                          SpoutWidth = 0;
		public int                          SpoutHeight = 0;
		public bool                         IsSpoutEnabled => SpoutWidth!=0 && SpoutHeight!=0 && IsWindows;
        private SpoutSender                 SpoutSender;

        [Header("Extra Rendered Scenes")]
		public List<ExtraRenderedScene>     ExtraRenderedScenes;                 // user defined, to trigger the whole extra rendering setup
		public List<RawImage>               ExtraRenderedScenesPlanes;           // prefab defined, with their own layer

        // Reference shaders to avoid them being stripped in builds        
        [Header("Global Shaders")]
        public List<Shader>                  shaders;    // 0: is the camera overlay

		// TRIAL settings (hard-coded)
		private TrialSettings               TrialSettings = new TrialSettings();

        public static bool IsWindows => (Application.platform == RuntimePlatform.WindowsPlayer || Application.platform == RuntimePlatform.WindowsEditor);
        public static bool IsMac => (Application.platform == RuntimePlatform.OSXPlayer || Application.platform == RuntimePlatform.OSXEditor);

		// Server
		public SharedObjectMap_Server<Model, API, SharedObjectMap_ServerAPIMethod> Server = new SharedObjectMap_Server<Model, API, SharedObjectMap_ServerAPIMethod>(
            _persistent_method_file: @"Assets\_SARGAME\_Core\_PM_Core\generated\apiwrapper_method_ids.json");


		private bool IsServerStarted = false;

        public static PMCore_Server Instance;

        // All server instantiate wrappers( PMWrapper components) are kept here 
        private Dictionary<Tabula.SharedObjectMap.GuidObject, IPMWrapper> PMWrappers = new Dictionary<Tabula.SharedObjectMap.GuidObject, IPMWrapper>();

        // Status of loading and instantiating model (from serialized json)
        public bool IsModelReady            { get; private set; }
		public bool IsModelAndWrappersReady { get; private set; }
		public bool IsScreenSetup           { get; private set; }

		//private ApplicationStatus Status = ApplicationStatus.Edit;
		public string Status { get; private set; }

       

        private RenderingSettings rendering_settings = new RenderingSettings();

        [Serializable]
        public class PrefabMapItem
        {
#if USE_SOLIDALLOY_TYPEREFERENCES
            [Inherits(typeof(Tabula.SharedObjectMap.GuidObject))]
            public TypeReference ModelClass; // the model class
#else
            // Without external package, it is just a string of the namespace.type .. must be exact!
            public string       ModelClass;
#endif
            public GameObject   Prefab;     // the prefab for the wrapper, IPMWrapper 
            public Transform    Container;  // optional container parenting the instantiated wrapper
        }

        // Entities that are spawned with prefab based on type
        [Serializable]
        public class EntityPrefabMapItem
        {
            public string               EntityType;     // the entity type
            public GameObject           Prefab;         // the prefab for the wrapper, IPMWrapper (usual method)
            public ScriptableObject     Config;         // a config object (GameEntity.Create()), if prefab is not defined
            public Transform            Container;      // optional container parenting the instantiated wrapper
        }

        // List of generic resources
        [Serializable]
        public class ResourceList
        {
            public string                 name;
            public List<ResourceItem>     items;
        }

        [Serializable]
        public class ResourceItem
        {
            public string                       name;       // id, lowecase..
            public string                       name_desc;  // descriptive name
            public UnityEngine.Object           resource;
		}

        private void Awake()
        {
            // Sentry
			ITransaction transaction = null;
			try
            {
                if (SentrySdk.IsEnabled)
                {
                    if (!string.IsNullOrEmpty(SentryTransaction))
                        transaction = SentrySdk.StartTransaction(SentryTransaction, "start");
                    else
                        Debug.LogError("SentryTransaction is not set!");
                }
            }
            catch { }

            Instance = this;

			#region App/License initialize

			try
			{
				if (LocalApp != null)
				{
					// will close app with return code if license is missing/failed

					// -666: license not found
					// -667: license not correct
					// -668: license feature not correct
					// -669: license check exception
					// -700: exception
					int localapp_ret = LocalApp.Initialize();

					if (localapp_ret == 1)
					{
						SARGAME = new SARGAME();
						SARGAME.Load();
					}
					else
					{
						// error or no license
						SARGAME = new SARGAME();

						if (Application.isEditor)
							Debug.LogError("***** INVALID LICENSE ****** You need it also in Unity!");

						//-100: cannot create LocalAppConfig
						//-101: no license or cannot be loaded
						//-102: license check failed
						//-103: license missing feature
						//-104: exception checking license
						switch (localapp_ret)
						{
							case -100: Debug.LogError("INVALID LOCALAPPCONFIG"); break;
							case -101: Debug.LogError("INVALID LICENSE, cannot be loaded"); break;
							case -102: Debug.LogError("INVALID LICENSE, check failed"); break;
							case -103: Debug.LogError("INVALID LICENSE, missing feature"); break;
							case -104: Debug.LogError("INVALID LICENSE, exception"); break;
						}

						return;
					}
				}
				else
					Debug.LogError("LocalApp not configured!");
			}
			catch (Exception ex)
			{
				// close the application with special error code
				Debug.LogException(ex);
				Application.Quit(-700);
				return;
			}

			#endregion

			DebugMode = OSUtilities.getCommandLineArgument("-debug");

            // TODO: future flag to enable keyboard players only
            TestingMode = OSUtilities.getCommandLineArgument("-test");

            // Disabling PM (for performance)
            if (OSUtilities.getCommandLineArgument("-disablepm"))
                DisablePM = true;

			// Disabling Server (for local-only no control installs)
			if (OSUtilities.getCommandLineArgument("-disableserver"))
				DisableServer = true;

			if (OSUtilities.getCommandLineArgument("-networkdiscovery_local"))
				DiscoveryMode |= NetworkDiscoveryMode.Local;

			if (OSUtilities.getCommandLineArgument("-networkdiscovery_p2p"))
                DiscoveryMode |= NetworkDiscoveryMode.P2P;

            if (OSUtilities.getCommandLineArgument("-networkdiscovery_off"))
                DiscoveryMode = NetworkDiscoveryMode.Off;

            // Spout rendering will inherently disable PM
            if (LocalApp.CheckLicenseFeature("business") || LocalApp.CheckLicenseFeature("spout"))
            {
                // setting spout size will set IsSpoutEnabled (which is true only on Windows)
                OSUtilities.getCommandLineArgument("-spout-width", ref SpoutWidth);
                OSUtilities.getCommandLineArgument("-spout-height", ref SpoutHeight);
            }

            if (IsSpoutEnabled)
                DisablePM = true;

            if (Application.isEditor)
            {
                TestingMode = true;
                DebugMode = true;
            }

            if (DebugMode)
            {
                Application.wantsToQuit += () =>
                {
					Debug.Log("Application.wantsToQuit");
                    SARGAME.App?.log("Application.wantsToQuit");

					return true;
                };

				Application.quitting += () =>
				{
					Debug.Log("Application.quitting");
					SARGAME.App?.log("Application.quitting");
				};

				Debug.Log("awake #1");

                SharedObjectMap.SharedObjectMap.Debug = true;
            }

			// Camera scale
			CameraSizeOriginal = Camera.orthographicSize;


			if (DebugMode)
				Debug.Log("awake #2");

            // if we have a license, log user
            string email = LocalApp.GetUserEmail();
            if (email != null)
                SentrySdk.ConfigureScope(scope =>
                {
                    scope.User = new User
                    {
                        Email = email
                    };
                });

			if (DebugMode)
				Debug.Log("awake #3");


			// Rendering settings
			if (rendering_settings.Get(gameObject))
			{
                rendering_settings.Dump();
				rendering_settings.OverrideFromCommandLine();

				rendering_settings.Apply(gameObject);
			}

			if (DebugMode)
				Debug.Log("awake #4");


			// default json settings
			JsonConvert.DefaultSettings = () => json_settings;

            // dump sysconf
            var sysdump = new StringBuilder();
            UnityUtilities.DumpSystemConfiguration(LocalApp.Product, sysdump);
            SARGAME.App.log(sysdump.ToString());

			if (DebugMode)
				Debug.Log("awake #5");

			// Get internal (PMCore) camera
			InternalCamera = transform.Find("camera").GetComponent<Camera>();

			// Makes sure a "PMCore" layer exists, and normalize all tags
			layer_pmcore = LayerMask.NameToLayer("PMCore");

			if (layer_pmcore == -1)
				throw new Exception("No PMCore layer defined!");

			if (DisablePM)
            {
				// Bypass projection mapping
                Camera.tag = "MainCamera";
				InternalCamera.gameObject.SetActive(false);

                // outputsurfaces will be created anyway but not rendered as they are in the PMCore layer
                // their existence is needed for the editor
			}
            else
            {
                InternalCamera.tag = "MainCamera";                         // this only must be the maincamera, to allow for line drawing
                InternalCamera.gameObject.layer = layer_pmcore;
                InternalCamera.cullingMask = (1 << layer_pmcore);
                InternalCamera.orthographicSize = Camera.orthographicSize; // match size! (TODO: also transform?)
                InternalCamera.gameObject.SetActive(true);

                Camera.cullingMask &= ~(1 << layer_pmcore); // removes PMCore layer from rendering on main camera
                Camera.tag = "Untagged";

                // Homography fix
                FlatWarpSurface.Camera = InternalCamera;
                FlatWarpSurface.HOMOGRAPHY_FIX = true;
                SARGAME.App.log($"HomographyFix: {FlatWarpSurface.HOMOGRAPHY_FIX}");

                SARGAME.App.log($"Camera={Camera.pixelWidth}x{Camera.pixelHeight} InternalCamera={InternalCamera.pixelWidth}x{InternalCamera.pixelHeight}");
			}

			if (DebugMode)
				Debug.Log("awake #6");

			// ALINE drawing allowed to rendertextures
#if USE_ALINE
			DrawingManager.allowRenderToRenderTextures = true;
#endif

			// Overlay
			Overlay.worldCamera = Camera;
			Overlay.planeDistance = 1.5f;

			// In Unity updates sent immediately (when modifying wrapper fields) must be inside a task, otherwise the app will hang
			Server.SendUpdatesInTask = true;

            BuildPrefabMaps();

			if (DebugMode)
				Debug.Log("awake #6");

			SARGAME.App.log("PMCore_Server: Awake finished");

			#region Server setup and Controller

			Server.onRPCServerStarted = (status, port) => IsServerStarted = status;
            Server.onCustomMessageReceived = CustomMessageReceived;

			#endregion

            // Start network discovery
            if (DiscoveryMode.HasFlag(NetworkDiscoveryMode.Local))
            {
                if (TryGetComponent(out NetworkDiscoveryServer))
                {
                    NetworkDiscoveryServer.EnsureServerIsInitialized();
                }
            }

			transaction?.Finish();
		}

        public void BuildPrefabMaps()
        {
            _prefabmap = new Dictionary<Type, PrefabMapItem>();
            _entityprefabmap = new Dictionary<string, EntityPrefabMapItem>();

            // hash prefab maps for speed
#if USE_SOLIDALLOY_TYPEREFERENCES
            foreach (PrefabMapItem i in PrefabMap)
                _prefabmap.Add(i.ModelClass.Type, i);
#else
            foreach (PrefabMapItem i in PrefabMap)
                if (!_prefabmap.TryAdd(Type.GetType(i.ModelClass), i))
                    Debug.LogError($"PMCore_Server.BuildPrefabMap() prefab={i.ModelClass} already existing");
#endif

			// Entity prefabs
			foreach (EntityPrefabMapItem e in EntityPrefabMap)
				if (!_entityprefabmap.TryAdd(e.EntityType, e))
					Debug.LogError($"PMCore_Server.BuildPrefabMap() entity={e.EntityType} already existing");
		}

        void Start()
        {
            // Servers starts always, independent from setting the model
            Task.Run(async () =>
            {
                try
                {
                    ToggleNetworking(EnableNetworking);

					Server.onLog = (s) =>
                    {
                        Tabula.Log.Logger.DefaultLog.WriteLine(s);
                        Debug.Log(s);
                    };

                    Server.onLogError = (s) =>
                    {
                        Tabula.Log.Logger.DefaultLog.WriteLine(s);
                        Debug.LogError(s);
                    };

                    Server.onLogException = (s, e) =>
                    {
                        Tabula.Log.Logger.DefaultLog.logException(s, e);
                        Debug.Log(s);
                        Debug.LogException(e);
                    };

                    // Hook events before loading moments (so views can be created/destroyed automatically)
                    Tabula.SharedObjectMap.SharedObjectMap.notifyObjectAdded = (o) => UnityMainThreadDispatcher.Instance().Enqueue(() => _OnObjectAdded(o));
                    Tabula.SharedObjectMap.SharedObjectMap.notifyObjectRemoved = (o) => UnityMainThreadDispatcher.Instance().Enqueue(() => _OnObjectRemoved(o));

                    if (!DisableServer)
                    {
                        if (!Server.Start())
                        {
                            // TODO: Visual showing the error for the builds!
                            Debug.LogError($"ERROR: Server cannot start!");
                        }
                    }

					UnityMainThreadDispatcher.Instance().Enqueue(() => LoadAndSetModel());
				}
                catch (Exception ex)
                {
                    Debug.LogException(ex);
                }
            });

        }

        private bool _last_is_window_foreground = false;
        private float time_check_window_is_foreground = 0;
        const float check_window_is_foreground_interval = 0.2f;
		public void Update()
		{
            // Trial logic
            TrialSettings.TrialLogic();

            // Win32 focus handling with native functions
            if (PMCore_Server.IsWindows)
            {
                if (Time.time > time_check_window_is_foreground)
                {
                    if (IsModelReady)
                    {
                        var focus = UnityUtilities.IsCurrentWindowForeground();
                        if (Wrapper.ModuleFocus != focus)
                            Wrapper.ModuleFocus = focus;
                    }

                    time_check_window_is_foreground = Time.time + check_window_is_foreground_interval;
                }
            }


            // Standard overlay message
            if (overlay_message!=null && overlay_message.gameObject.activeInHierarchy)
                if (Time.time > overlay_message_time_end)
                    SetStandardMessage(null);
        }

		public void OnGUI()
		{
            // will draw directly to screen only id DisablePM = true
            // otherwise the TrialLogic() handles adding/removing the commandbuffer to the camera event
            TrialSettings.TrialDraw_ScreenDirect();
		}


		// NOTE: this does not seem really realiable on WIN32, checking it in update
		private void OnApplicationFocus(bool focus)
        {
            if (PMCore_Server.IsMac)
            { 
                if (!IsModelReady)
                    return;

                Wrapper.ModuleFocus = focus;
			}
		}

		private void OnApplicationQuit()
        {
            try
            {
                Server?.Stop();
                NetworkDiscoveryServer?.CloseServerUdpClient();
            }
            catch { }
        }

        public void ToggleNetworking(bool toggle)
        {
            if (EnableNetworking == toggle)
                return;

#if NO_LICENSE
            // TODO: testing serial?
            SARGAME.App.logError("PMCore_Server: NO_LICENSE is defined, cannot toggle networiking (TODO: testing serial)");
            return;
#endif

			var license_serial = LocalApp_Scriptable.Instance?.LicenseInfo?.Serial;

			if (string.IsNullOrEmpty(license_serial))
			{
				SARGAME.App.logError("PMCore_Server: Cannot obtain license serial");
				return;
			}

			AuthCode = null;
			AdminAuthCode = null;

			if (!toggle)
            {
                _p2pInstance?.StopServer();

				EnableNetworking = false;
            }
            else
            {
				EnableNetworking = true;

                // Admin authcode is retrieved in any case, playing is not required
#pragma warning disable CS4014 // Because this call is not awaited, execution of the current method continues before the call is completed
                Task.Run(() =>
                {
                    string adminauthcode = null;

                    try
                    {
                        adminauthcode = LicenseActivatorLib.GetAuthCode(license_serial, admin: true, use_legacy_hid: true);
                        if (string.IsNullOrEmpty(adminauthcode))
                            throw new Exception($"Cannot get admin auth code for serial: {license_serial}");
                    }
                    catch (Exception ex)
                    {
                        SARGAME.App.logException("GetAuthCode() ADMIN", ex);
                        adminauthcode = null;
                    }

                    AdminAuthCode = adminauthcode;
                });
#pragma warning restore CS4014 // Because this call is not awaited, execution of the current method continues before the call is completed

                // Start the P2P service in the local port, publishing the auth-code
#pragma warning disable CS4014 // Because this call is not awaited, execution of the current method continues before the call is completed
                Task.Run(() =>
                {
                    string authcode = null;

                    try
                    {
                        authcode = LicenseActivatorLib.GetAuthCode(license_serial, admin: false, use_legacy_hid: true);
                        if (string.IsNullOrEmpty(authcode))
                            throw new Exception($"Cannot get auth code for serial: {license_serial}");
                    }
                    catch (Exception ex)
                    {
                        SARGAME.App.logException("GetAuthCode()", ex);
                        authcode = null;
                    }

                    AuthCode = authcode;

                    // P2P server start action
                    Action p2p_server_start = null;

                    //P2P error handling with reconnections
                    Action<P2P.ServerData> p2p_onerror = null;

                    p2p_onerror = async (P2P.ServerData server_data) =>
                    {
						SARGAME.App.log($"P2P RESULT:{server_data.status} public_ip:{server_data.ip} public_port:{server_data.port} join_code:{server_data.join_code}");

						switch (server_data.status)
						{
                            case P2P.ServerData.State.ConnectedAndPublished:
                                P2PServerPublishTries = 0;
                                break;

							case P2P.ServerData.State.ServerOfferFailed:
							case P2P.ServerData.State.ServerFatalError:
							case P2P.ServerData.State.MatchUpLostConnection:

                                P2PServerPublishTries++;

                                if (P2PServerPublishTries < P2PServerPublishMaxTries)
                                {
									SARGAME.App.log($"P2P Reconnection try:{P2PServerPublishTries}");

									await Task.Delay(1000);

									p2p_server_start.Invoke();
								}
                                else
                                {
									SARGAME.App.logError($"P2P Max reconnection tries reached:{P2PServerPublishMaxTries}, giving up!");
								}

								break;
						}
					};

                    // NOTE: P2P publishing is needed only for playing now (still no editor)
                    // Initialize P2P instance if not already created
                    if (_p2pInstance == null)
                        _p2pInstance = new P2P();

                    p2p_server_start = () => _p2pInstance.StartAndPublishServer(ClientServer_Constants.RPCServerPort, authcode, "server", p2p_onerror);

                    p2p_server_start.Invoke();
				});
#pragma warning restore CS4014 // Because this call is not awaited, execution of the current method continues before the call is completed
             
            }
		}

		// little helper to be moved
		public static void FindTransformsFromHierarchyByName(string name, List<Transform> found_transforms, Transform parent)
		{
			Transform child = parent.Find(name);

			if (child != null)
                found_transforms.Add(child);

			foreach (Transform c in parent)
				FindTransformsFromHierarchyByName(name, found_transforms, c);
		}

		#region Model Add/Remove

		private void _OnObjectAdded(Tabula.SharedObjectMap.GuidObject guid_obj)
        {
            // NEW: normalization now is for every GuidObject also at load/create time
            guid_obj.Normalize();

            IPMWrapper wrapper = null;

            // Uses the map to create the wrapper structure if specified
            if (_prefabmap.TryGetValue(guid_obj.GetType(), out PrefabMapItem witem))
            {
                var o = Instantiate(witem.Prefab, witem.Container);

                wrapper = o.GetComponent<IPMWrapper>();
                wrapper.SetModel(guid_obj);

                AddPMWrapper(guid_obj, wrapper);
            }
            else if (guid_obj is Entity)
            {                
                // Entities are polymorphic and their prefab will depend on "type" field, looking up in a special table
                // IMPORTANT: if name is prepend with # the it is a SCENE REFERENCE of an object in hierarchy
                var entity = guid_obj as Entity;

                if (entity == null)
                    return;
               
                if (entity.name.StartsWith("#"))
                {
                    // SCENE REFERENCE: must exist in scene, and therefore a IPMWrapper component must be already added, the model will be linked to the serialized entity
                    // NOTE: must be active in scene!!! otherwise look for Resources.FindObjectsOfType

                    var entity_name = entity.name.Replace("#", "");

					// Search in "Entities" hierarchy, which must exist!
					// TODO: Still useful? now in Server_Local.BeforeSetModel() absolute entities are removed from the model before setting it!
					var entities_container = GameObject.Find("Entities");
                    if (entities_container == null)
                    {
						// not found
						Debug.LogError($"PMCore_Server:  cannot search for entity reference with name {entity.name}, no 'Entities' container found!");
						return;
					}

                    List<Transform> found_transforms = new List<Transform>();

					FindTransformsFromHierarchyByName(entity_name, found_transforms, entities_container.transform);

                    if (found_transforms.Count == 0)
                    {
						// not found
						Debug.LogWarning($"PMCore_Server: entity reference with name {entity.name} not found in scene, or not active at scene start (1)");
						return;
					}

                    // Select the ones with IPMWrapper components
                    var ipmwrappers = (from t in found_transforms where t.gameObject.TryGetComponent(out IPMWrapper c) select t).ToList();

					if (ipmwrappers.Count == 0)
					{
						// not found
						Debug.LogWarning($"PMCore_Server: entity reference with name {entity.name} not found in scene, or not active at scene start (2)");
						return;
					}
                    else if (ipmwrappers.Count > 1)
                    {
						// too many
						Debug.LogWarning($"PMCore_Server: multiple entity references with name {entity.name} found in scene");
						return;
					}

                    // Check a component is added
                    wrapper = ipmwrappers.FirstOrDefault().GetComponent<IPMWrapper>();
                    if (wrapper == null)
					{
                        Debug.LogWarning($"PMCore_Server: No IPMWrapper component found for entity referenced with name {entity.name}");
                        return;
                    }

                    // setting the model
                    wrapper.SetModel(guid_obj); // object will not be destroyed when model is destroyed
                    AddPMWrapper(guid_obj, wrapper);                    
                }
                else
                {
                    // Entity to be instantiated with prefab or config

                    if (entity.type != null)
                        if (_entityprefabmap.TryGetValue(entity.type, out EntityPrefabMapItem weitem))
                        {
                            // Always clone for safety

                            ScriptableObject config = null;

                            if (weitem.Config != null)
                                switch (weitem.Config)
                                {
                                    case BaseEntityConfig bec:
                                        config = bec.Clone();
                                        (((BaseEntityConfig)config)).entity_model_settings.guid = guid_obj.__guid;    // if it's an entity config, set the guid for later initialization
                                        break;

                                    default:

                                        // basic cloning
                                        config = Instantiate(weitem.Config);
                                        break;
                                }

                            GameObject o = null;

                            if (weitem.Prefab != null)
                            {
                                // This is the low-level creation for generic objects
                                // If a config is provided, it should try to configure it
                                o = Instantiate(weitem.Prefab, weitem.Container);                              

                                // if the config is provided and the objects supports it let's create it (it will auto add to the pmwrappers)
                                if (o!=null && config!=null && o.TryGetComponent(out IInitializableWithConfig initializable))
                                    initializable.InitializeWithConfig((BaseEntityConfig) config);
                                else
                                {
									// another case is a standard behaviour with wrapper, just set its model
									if (o != null)
									{
										if (o.TryGetComponent<IPMWrapper>(out wrapper))
										{
											wrapper.SetModel(guid_obj);
											wrapper.SetFlags(IPMWrapper.WrapperFlags.DestroyObjectWhenUnallocated);
											AddPMWrapper(guid_obj, wrapper);
										}
									}
								}

                            }
                            else if (config != null && OnEntityCreateFromConfig!=null)
                            {
                                // standardized entity creation, the method must have been registered by the GameManagerBase
                                // It will call entity's InitializeWithConfig() that will ALSO configure the PMWrapper
                                var ipmentity = OnEntityCreateFromConfig(config);

                                if (ipmentity != null)
                                {
                                    o = ipmentity.GetGameObject();

                                    // TODO: sequential naming
                                    o.name = entity.name;

                                    if (weitem.Container != null)
                                        o.transform.SetParent(weitem.Container, true);
                                }
                            }
                            else
                            {
                                LocalAppConfig.Instance?.logError($"PMCore_Server: OnObjectAdded() cannot create entity type={entity.type}");
							}

                            // Pevious PMWrapper adding
                            /*
                            if (o != null)
                            {
                                if (o.TryGetComponent<IPMWrapper>(out wrapper))
                                {
                                    wrapper.SetModel(guid_obj);
                                    wrapper.SetFlags(IPMWrapper.WrapperFlags.DestroyObjectWhenUnallocated);

                                    AddPMWrapper(guid_obj, wrapper);
                                }
                                else
									LocalAppConfig.Instance?.logError($"PMCore_Server: OnObjectAdded() cannot get IPMWrapper from created entity type={entity.type}");
							}
                            */
                        }
                }
            }
            else
            {
                // TODO: Otherwise call a handler for special cases
            }

            OnObjectAdded?.Invoke(guid_obj, wrapper);
        }

        private void _OnObjectRemoved(Tabula.SharedObjectMap.GuidObject guid_obj)
        {
            IPMWrapper removed_wrapper = null;

            // Will query for particulare interface and destroy the underlying object
            RemovePMWrapper(guid_obj, ref removed_wrapper);

            OnObjectRemoved?.Invoke(guid_obj, removed_wrapper);
        }

		#endregion

		#region Entities

		// Generalized removing an entity
		public void RemoveEntity(Entity e)
        {
            var parent = e.__Parent;
            if (parent != null)
            {
                if (parent is Entity)
                {
                    var parent_entity = parent as Entity;
					var w = SharedObjectMap.SharedObjectMap.getWrapperFromModel<Entity, EntityWrapper>(parent_entity);
                    var index = parent_entity.items.IndexOf(e);
                    w.items_RemoveAt(index);
				}
                else
                {
                    // top level entity
                    var parent_root = parent as Model;
					var w = SharedObjectMap.SharedObjectMap.getWrapperFromModel<Model, ModelWrapper>(parent_root);
					var index = parent_root.Entities.IndexOf(e);
					w.Entities_RemoveAt(index);
				}
            }			
		}

		#endregion

		#region Resources

        public ResourceList GetResourceList(string list_name)
        {
			foreach (var rl in ResourceMap)
			{
				if (rl.name == list_name)
				{
                    return rl;
				}
			}

            return null;
		}

        public IEnumerable<T> GetResources<T>(string list_name) where T : UnityEngine.Object
        {
            var rl = GetResourceList(list_name);
            if (rl == null)
                return Enumerable.Empty<T>();

			var ei = rl.items.OfType<T>();

			if (ei != null)
				return ei;
			else
				return Enumerable.Empty<T>();
		}

        public T GetResourceItem<T>(string list_name, string item_name) where T : UnityEngine.Object
        {
			var rl = GetResourceList(list_name);
            if (rl == null)
            {
                Debug.LogError($"GetResourceItem() list \"{list_name}\" not found ");
                return default;
            }

            var item = (from i in rl.items where i.name == item_name select i.resource).FirstOrDefault();
            if (item == null)
				Debug.LogError($"GetResourceItem() item \"{item_name}\" not found ");

			return (T) item;
		}

		#endregion

		public void ApplicationStatusChange(string new_status)
        {
            Debug.Log($"PMCore_Server: ApplicationStatusChange({new_status})");
            Status = new_status;

            // The game must register here to apply changes
            OnApplicationStatusChanged?.Invoke(new_status);

            // Signal change to all wrappers            
            foreach (var w in PMWrappers.Values)
                w.OnApplicationStatusChanged(new_status);                                    
        }

        #region Model 

        public void LoadAndSetModel(Model _model = null)
		{
            StartCoroutine(LoadAndSetModelCR(_model));
		}

        public IEnumerator LoadAndSetModelCR(Model _model = null)
        {
            if (_model == null)
			{
                // Priority to the commandline requested project file
                string project = null;
                if (OSUtilities.getCommandLineArgument("-project", ref project))
                    SerializedModelPath = File.Exists(project) ? project : null;

                // Scenario override (screen an structures), it is just an id
                OSUtilities.getCommandLineArgument("-scenario", ref ScenarioId);

				if (string.IsNullOrEmpty(SerializedModelPath))
                    yield break;

                if (Application.isEditor)
                {
                    if (LoadModelInEditor && File.Exists(SerializedModelPath))
                    {
                        // Model is loaded on the server (for testing)
                        _model = DeserializeFromJson<Model>(SerializedModelPath);
                    }
                }
                else if (LoadModelInBuild && File.Exists(SerializedModelPath))
                {
                    // Model is loaded on the server (for testing)
                    _model = DeserializeFromJson<Model>(SerializedModelPath);
                }
                
                if (_model==null && File.Exists(EmptyModel))
				{
                    // Model will be sent by the client, in the meantime use a temporary empty model
                    _model = DeserializeFromJson<Model>(EmptyModel);
                    _model.Normalize();
                }

                if (_model == null)
                {
                    // Should not happen?
                    _model = new Model();
                    _model.SetDefaults();
                }


                // Scenario override
                if (!string.IsNullOrEmpty(ScenarioId))
                {
                    var scenario = SARGAME.Instance.GetScenario(ScenarioId);
                    if (scenario != null)
                    {
                        scenario.ApplyToModel(_model);
                        
                        // link with scenario
                        _model.ScenarioId = ScenarioId;
                    }
                    else
                        LocalApp.App.logError($"PMCore_Server.LoadAndSetModel(), scenario not found: {ScenarioId}");
                }

				LocalApp.App.log($"Model.Screen.size={_model.Screen.size.width}x{_model.Screen.size.height}");



              
                if (IsSpoutEnabled)
                {
					LocalApp.App.log($"Spout output overriding model screen size to: {SpoutWidth}x{SpoutHeight}");

					_model.Screen.size.width = SpoutWidth;
					_model.Screen.size.height = SpoutHeight;

					// NOTE: this won't be saved
				}
				else
                {
					// The screen size must always be the one created, otherwise thing just don't work
					if (_model.Screen.size.width != UnityEngine.Screen.width || _model.Screen.size.height != UnityEngine.Screen.height)
                    {
                        LocalApp.App.log($"Detected screen size different from project, resetting to: {UnityEngine.Screen.width}x{UnityEngine.Screen.height}");

                        _model.Screen.size.width = UnityEngine.Screen.width;
                        _model.Screen.size.height = UnityEngine.Screen.height;

                        // NOTE: this won't be saved
                    }
                }

			}
                       
            // Chance to modify the model before it is set and PMWrappers instantiated
            BeforeSetModel?.Invoke(_model);

            // Setting the model version from SARGAME.Version, identifies compatibility in the model/RPC
            _model.Version = SARGAME.Version;

            // Since SetModel() will invoke actions to create entities and wrappers in queue, make sure here we are not rate-limited
            UnityMainThreadDispatcher.Instance().EnableMaxTimePerExecution = false;

            SetModel(_model);

			IsModelReady = true;                // Loaded Model is ready, but PMWrappers are still to be created (in queue)

			//  wait for main thread to be emtpy, so that all wrappers are created
			yield return UnityMainThreadDispatcher.Instance().WaitQueueIsEmptyCR();

            _model.AfterModelLoaded();

            IsModelAndWrappersReady = true;     // All wrappers have been created
           
            SetupScreen();  // sets IsScreenSetup

			OnModelCreated?.Invoke();

			// Issue first play
			ApplicationStatusChange("play");

            // LAST CHECK: add other guidobject that may havve been dinamically created            
            /*
            var op = SharedObjectMap.SharedObjectMap.AddAllGuidObjects(PMCore_Server.Instance.Model);
            foreach (var g in op.GuidObjects)
                Debug.Log($"PMCore_Server: LAST ADDITION {g}");            
            */


            UnityMainThreadDispatcher.Instance().EnableMaxTimePerExecution = true;

			// Signal that all is ready, when server has started
			if (!DisableServer)
                while (!IsServerStarted)
                {
                    SARGAME.App.log("Waiting for Server to start");
                    yield return new WaitForSecondsRealtime(0.5f);
                }

            SARGAME.Instance.WriteProcessInfo(package_name: LocalApp.Package, module_name: LocalApp.Module);

            // if a background video is requested create it
            if (!string.IsNullOrEmpty(Model.BackgroundVideoPath))
                SetupBackgroundVideo();
        }

        // Keeps the same project and just replaces structures and output surfaces
        public void LoadWall(Model _model)
		{
            IEnumerator _load_wall_cr()
			{
                // directly load in model, this will destroy previous ones and create new wrappers                
                Wrapper.Structures_Set(_model.Structures);
                Wrapper.OutputSurfaces_Set(_model.OutputSurfaces);

                yield return UnityMainThreadDispatcher.Instance().WaitQueueIsEmptyCR();

                SetupScreen();                
            }


            StartCoroutine(_load_wall_cr());
		}

        public void SetModel(Model model)
        {
            // The setter will initialize as Server and generate all OnObjectAdded events
            Server.Model = model;
            Wrapper = Tabula.SharedObjectMap.SharedObjectMap.GetRootWrapper<ModelWrapper>();

            UnityMainThreadDispatcher.Instance().Enqueue(() => OnServerConfigure?.Invoke());            
        }

        public void SaveModel()
        {
            if (!string.IsNullOrEmpty(SerializedModelPath))
                SerializeToJson(Model, SerializedModelPath);

            // If the model has a reference to a ScenarioID, this is updated as well
            // Calibrator just saves to provided configuration file
            if (!string.IsNullOrEmpty(Model.ScenarioId))
            {
                var scenario = SARGAME.Instance.GetScenario(Model.ScenarioId);
                if (scenario == null)
                {
                    LocalApp.App.logError($"PMCore_Server.SaveModel() provided scenarioId cannot be found: {Model.ScenarioId}");
                    return;
                }

                scenario.UpdateFromModel(Model);
                scenario.Save();
            }
        }

        #endregion

        #region Calibration

		public CalibrationSnapshot GetCalibrationInfo()
        {
			var s = new CalibrationSnapshot();

			s.screen_full = new SizeI()
            { 
                width = Model.Screen.size.width,
                height = Model.Screen.size.height 
            };

			s.screen_crop = Model.GetVisualScreenSize();

			s.markers = new List<Vector2i>();
			foreach (var c in Model.Screen.screen_markers)
			{
				s.markers.Add(new Vector2i() { x = (int)c.position.x, y = (int)c.position.y });
			}

            /*
			DateTimeOffset now = DateTimeOffset.UtcNow;
			DateTimeOffset epochStart = new DateTimeOffset(1970, 1, 1, 0, 0, 0, TimeSpan.Zero);
			long epochTime = (long)(now - epochStart).TotalSeconds;

			SerializeToJson(s, $"calibration_{epochTime}.json");
            */

            return s;
		}

        #endregion

        #region Screen and Output Surfaces

		public void SetupScreen()
        {
            if (IsSpoutEnabled)
            {
				// TODO: check again for feature
				Debug.Log($"SetupScreen() with SpoutEnabled=true Screen={SpoutWidth}x{SpoutHeight}");

				SpoutSender = GetComponent<SpoutSender>();
				if (SpoutSender == null)
				{
					Debug.LogError($"SetupScreen(): cannot create Spout sender");
                    goto screen_setup_end;
				}

				if (OutputRenderTexture != null)
					Destroy(OutputRenderTexture);

				OutputRenderTexture = new RenderTexture(SpoutWidth, SpoutHeight, 0, RenderTextureFormat.ARGB32);

				Camera.targetTexture = OutputRenderTexture;

                SpoutSender.captureMethod = CaptureMethod.Texture;
				SpoutSender.sourceTexture = OutputRenderTexture;
                SpoutSender.enabled = true;
			}
            else if (!DisablePM)
            {
                // NORMAL

				// Create the rendertexture for the screen output, can be any size since it will be mapped to different surfaces
				if (OutputRenderTexture != null)
                    Destroy(OutputRenderTexture);

                OutputRenderTexture = new RenderTexture(Model.Screen.size.width, Model.Screen.size.height, 0, RenderTextureFormat.ARGB32);

                Camera.targetTexture = OutputRenderTexture;

				LocalApp.App.log($"SetupScreen() Camera={Camera.pixelWidth}x{Camera.pixelHeight}  OutputRenderTexture={OutputRenderTexture.width}x{OutputRenderTexture.height}");
			}
            else
            {
				Debug.Log($"SetupScreen() with DisablePM=true Screen={UnityEngine.Screen.width}x{UnityEngine.Screen.height}");
			}

            // NOTE: Each OutputSurface directly controls a FlatWarpSurface with the PMOutputSurfaceWrapper

            // THe Model must have created at least an OutputSurface
            OutputSurfaces =  PMWrappers.Values.OfType<PMOutputSurfaceWrapper>().ToList();
            
            if (OutputSurfaces.Count == 0)
			{
                // TODO: create a single one?
                Debug.LogError("No OutputSurfaces created in model!");
                goto screen_setup_end;
			}

            // set layer to PMCore
            foreach (var os in OutputSurfaces)
                os.SetLayer(layer_pmcore);

            if (!DisablePM)
                OutputSurfaces[0].Texture = OutputRenderTexture;

            // set all edges to screen aspect
            ScreenEdge.AdjustAllEdges();

            SetupExtraRenderedScenes();

            // Prepare the special overlay text message
            CreateStandardOverlayMessage();

		screen_setup_end:

            IsScreenSetup = true;

            // Now all the structures must be updated as they are waiting to create their meshes / materials, before they are accessed
            foreach (var s in GetStructures())
                s.Update(); // call update manually
        }

        #endregion

        #region Extra Rendered Scenes

        // If the user populated the ExtraRenderedScenes list, they are assigned the layers and rendered by extra_cameras

        // Enum uses real layer integer values
        public enum ExtraCameraLayers
        {
            None = 0,
            ExtraCamera1 = 6,
			ExtraCamera2 = 7

            // TODO: other layers can be added
		}

        public void SetupExtraRenderedScenes()
        {
            var used_layers = new Dictionary<int, ExtraRenderedScene>();

            // Setup scenes setting layers for whole hierarchy
            foreach (var rs in ExtraRenderedScenes)
            {
                used_layers.Add((int)rs.CameraLayer, rs);
                rs.Setup();
            }

            // Create RTT for each Camera
            foreach (var p in ExtraRenderedScenesPlanes)
            {
                if (used_layers.Keys.Contains(p.gameObject.layer))
                {
                    p.gameObject.SetActive(true);
                    p.gameObject.transform.parent.gameObject.SetActive(true);   // the canvas
                    p.texture = used_layers[p.gameObject.layer].CameraTexture;

                    // alpha is 0 by defauls
                    p.color = new Color(p.color.r, p.color.g, p.color.b, 1f);
                }
            }

        }

        #endregion

        #region Structures

		public IEnumerable<PMStructureWrapper> GetStructures()
            => PMWrappers.Values.OfType<PMStructureWrapper>();

        public bool IsScreenPointInStructures(Vector2 spos)
        {            
			foreach (var s in GetStructures())
            {
                if (s.IsScreenPointInStructure(spos))
                    return true;
			}

            return false;
        }

        [ContextMenu("RenderStructuresToTexture")]
        public void test_RenderStructuresToTexture()
        {
            RenderStructuresToTexture(image_path: @"c:\tmp\structures.png");
        }

		public Texture2D RenderStructuresToTexture(int single_structure = -1, string image_path = null, bool create_texture = true)
		{
			// Create a bitmap with the specified canvas size
			using (System.Drawing.Bitmap bitmap = new System.Drawing.Bitmap(Model.Screen.size.width, Model.Screen.size.height))
			{
				// Set the bitmap to have a transparent background
				using (System.Drawing.Graphics graphics = System.Drawing.Graphics.FromImage(bitmap))
				{
					graphics.Clear(System.Drawing.Color.Transparent);

					// Disable antialiasing for crisp borders
					graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.None;

					// Create a brush to fill the polygons
					using (System.Drawing.Brush brush = new System.Drawing.SolidBrush(System.Drawing.Color.White))
					{
                        int i = 0;
						foreach (var s in GetStructures())
						{
                            if (single_structure != -1)
                            {
                                if (single_structure != i)
                                {
                                    i++;
                                    continue;
                                }
                            }

							Vector2[] verts = new Vector2[s.Model.vertices.Count];
                            s.GetVerticesToUnityScreenSpace(ref verts);

							// Convert the polygon points from Vector2 to PointF
							System.Drawing.PointF[] points = verts.Select(p => new System.Drawing.PointF(p.x, Model.Screen.size.height - p.y)).ToArray();
							graphics.FillPolygon(brush, points);

                            i++;
						}
					}
				}

				// saving the bitmap as a PNG file
                if (!string.IsNullOrEmpty(image_path))
				    bitmap.Save(image_path, System.Drawing.Imaging.ImageFormat.Png);

                // Convert the bitmap to a byte array
                if (create_texture)
                    using (MemoryStream memoryStream = new MemoryStream())
                    {
                        bitmap.Save(memoryStream, System.Drawing.Imaging.ImageFormat.Png);
                        byte[] imageBytes = memoryStream.ToArray();

                        // Create a Texture2D and load the image bytes into it
                        Texture2D texture = new Texture2D(2, 2);
                        texture.LoadImage(imageBytes);
                        texture.Apply();

                        return texture;
                    }
                else
                    return null;
			}
		}

		#endregion

        #region PMWrappers handling

		public void AddPMWrapper(Tabula.SharedObjectMap.GuidObject guid_obj, IPMWrapper w) 
            => PMWrappers.Add(guid_obj, w);

        public void RemovePMWrapper(Tabula.SharedObjectMap.GuidObject guid_obj, ref IPMWrapper removed_wrapper)
        {
            if (PMWrappers.TryGetValue(guid_obj, out IPMWrapper w))
            {
                if (removed_wrapper != null)
                    removed_wrapper = w;
                w.DestroyModel();
                PMWrappers.Remove(guid_obj);
            }
        }

        public T GetPMWrapperWithGuid<T>(long guid) where T : IPMWrapper
		{
			if (guid == -1)
				return default;

			var pm = (from p in PMWrappers.Values.OfType<T>() where p.GetModel().__guid == guid select p).FirstOrDefault();

            return pm;
        }

		public T GetPMWrapperWithModel<T>(SharedObjectMap.GuidObject m) where T : IPMWrapper
		{
            if (m==null || m.__guid == -1)
                return default;

			var pm = (from p in PMWrappers.Values.OfType<T>() where p.GetModel() == m select p).FirstOrDefault();

			return pm;
		}

		public IEnumerable<T> GetWrappers<T>() where T: IPMWrapper
            => PMWrappers.Values.OfType<T>();

        public IEnumerable<T> GetWrappersWithFlag<T>(WrapperFlags flag) where T : IPMWrapper
          => PMWrappers.Values.OfType<T>().Where(a => a.GetFlags().HasFlag(flag));

		public T GetWrapper<T>() where T : IPMWrapper
		   => GetWrappers<T>().FirstOrDefault();

        public IEnumerable<PMEntityWrapper> GetEntityWrappers()
            => GetWrappers<PMEntityWrapper>();

		/*
        public void RemoveAllPMWrappers()
        {
            foreach (var w in PMWrappers.Values)
            {
                w.DestroyModel();
            }

            PMWrappers.Clear();
        }
        */

		// TODO: create extension methods for model / wrappers?
		public IPMWrapper GetPMWrapper(Tabula.SharedObjectMap.GuidObject guid_obj)
		{
            if (PMWrappers.TryGetValue(guid_obj, out IPMWrapper w))
                return w;
            else
                return null;
        }

        #endregion

        #region Background Video

        private void SetupBackgroundVideo()
        {
            if (Model==null || string.IsNullOrEmpty(Model.BackgroundVideoPath))
                return;

            var localpath = new Uri(Model.BackgroundVideoPath).LocalPath;

			if (!File.Exists(localpath))
            {
                Debug.LogError($"SetupBackgroundVideo() cannot find {localpath}");
                return;
            }

			Debug.Log($"SetupBackgroundVideo() setting video to {localpath}");

			try
            {
                BackgroundCanvas.gameObject.SetActive(true);

                if (BackgroundVideoTexture != null)
                    Destroy(BackgroundVideoTexture);

                BackgroundVideoTexture = new RenderTexture(Model.Screen.size.width, Model.Screen.size.height, 0, RenderTextureFormat.ARGB32);
                BackgroundVideoPlayer.targetTexture = BackgroundVideoTexture;
                BackgroundVideoImage.texture = BackgroundVideoTexture;

                BackgroundVideoPlayer.url = Model.BackgroundVideoPath;
                BackgroundVideoPlayer.Play();
            }
            catch(Exception ex)
            {
				BackgroundCanvas.gameObject.SetActive(false);
				Debug.LogException(ex);
			}

		}

        #endregion

        #region Networking

		// In order to be quick at receiving messages PMCore_Server detects for controllers data and stores them in a dictionary

		public class ClientControllerData
        {
            public DateTime                 time_last_message;  // time of message reception

            public int                      connection_id;
            public PlayerControllerMessage  message;
        }

        // indexed by player
        public ConcurrentDictionary<int, ClientControllerData> ClientControllersData = new System.Collections.Concurrent.ConcurrentDictionary<int, ClientControllerData>();

        // list of banned connection ids
        public HashSet<int> BannedConnections = new HashSet<int>();

		// NOTE: this is not on the Unity thread!
		public void CustomMessageReceived(Tabula.RPC.Shared.KcpMessage msg)
		{
            ClientControllerData cdata = default;

            // skip banned connections (a Server restart will be needed)
            if (BannedConnections.Contains(msg.header.connection_id))
                return;

            try
            { 
			    // here we will process messages
			    switch (msg.header.code)
			    {
                    case PlayerControllerMessage.KCPMSG_PLAYERCONTROLLER_PING:
						// send back immediately to calculate roundtrip

						var kcpserver = Server.ServerRPC.Server.NativeServer;

                        // no payload
						var m = new Shared.KcpMessage()
						{
							header = new Shared.KcpMessage._header()
							{
								code = PlayerControllerMessage.KCPMSG_PLAYERCONTROLLER_PING,
								chunk_sequence = 1,
								chunk_count = 1,
							}
						};

						lock (kcpserver)
						{
							kcpserver?.Send(msg.header.connection_id, new ArraySegment<byte>(m.ToByteArray()), kcp2k.KcpChannel.Unreliable);
						}

						break;

				    case PlayerControllerMessage.KCPMSG_PLAYERCONTROLLER_DATA:

					    var msg_controller = PlayerControllerMessage.DecodeFromPayload(msg.payload);

                        // Check message version
                        if (msg_controller.version != PlayerControllerMessage.Version)
                        {
                            BannedConnections.Add(msg.header.connection_id);
                            LocalAppConfig.Instance?.logError($"PlayerControllerMessage: received bad version {msg_controller.version}!={PlayerControllerMessage.Version} from connection_id={msg.header.connection_id}, it has been banned");
                            return;
					    }

					    if (ClientControllersData.TryGetValue(msg_controller.player_id, out cdata))
                        {
                            cdata.connection_id = msg.header.connection_id;
                            cdata.message = msg_controller;
                            cdata.time_last_message = DateTime.Now;
                        }
                        else
                        {
                            cdata = new ClientControllerData()
                            {
                                connection_id = msg.header.connection_id,
                                message = msg_controller,
                                time_last_message = DateTime.Now
                            };

                            ClientControllersData.TryAdd(msg_controller.player_id, cdata);
                        }

					    break;

                    default:
                        OnCustomMessageReceived?.Invoke(msg);
                        break;
			    }
		    }
            catch(Exception ex)
            {
                LocalAppConfig.Instance?.logException("CustomMessagereceived()", ex);
			}
        }

        #endregion

        #region Contract Generation

		[ContextMenu("Generate Contracts")]
        public void GenerateContracts()
        {

#if UNITY_EDITOR

            
            string folder_generated = Path.Combine("Assets/_SARGAME/_Core/_PM_Core/generated");

            Server.GenerateContracts(new SharedObjectMap_Server<Model, API, SharedObjectMap_ServerAPIMethod>.ContractOptions()
            {
                NameSpace = "Tabula.PMCore",
                ClientClassName = "PMCore_Client",

                OutputFolder_RPC = folder_generated,
                OutputFolder_OSC = null,
                OutputFolder_Wrapper = folder_generated,
                OutputFolder_View = folder_generated
            });

            // Generate enums for PMWrapper classes, to ease model -> wrapper mapping
            /*
            var wrapper_types = Reflection.Utilities.FindAllDerivedTypes<IPMWrapper>(new Reflection.Utilities.Options()
            {
                IncludeGenerics = false
            }).ToList();

            var cg = new Reflection.CodeGeneration();

            cg.Usings.Add("System");
            cg.Usings.Add("System.Collections.Generic");
            cg.Namespace = "Tabula.PMCore";

            var w_enum = new Reflection.CodeGeneration.CGEnum() { Name = "PMWrapper_Classes" };
            w_enum.Attributes.Add("Serializable");
            foreach (var wt in wrapper_types)
                w_enum.Enums.Add(wt.Name, null);

            cg.Elements.Add(w_enum);

            var w_class = new Reflection.CodeGeneration.CGClass() { Name = "WrappersExtensions", Partial = true, Static = true };
            var w_wdict = new Reflection.CodeGeneration.CGDictionary() { Name = "PMWrappersMap", Static = true };
            w_wdict.KeyType = "PMWrapper_Classes";
            w_wdict.ValueType = "System.Type";
            foreach (var wt in wrapper_types)
                w_wdict.Values.Add($"PMWrapper_Classes.{wt.Name}", $"typeof({wt.FullName})");

            w_class.Elements.Add(w_wdict);

            cg.Elements.Add(w_class);


            cg.Generate(Path.Combine(folder_generated, "wrappers_utilities.cs"));
            */

            AssetDatabase.Refresh();
#endif
        }

        #endregion

        #region Project management

        [ContextMenu("Reload Project")]
        public void ReloadProject()
        {
            if (!string.IsNullOrEmpty(SerializedModelPath))
            {
                if (File.Exists(SerializedModelPath))
                {
                    var _model = DeserializeFromJson<Model>(SerializedModelPath);

                    SetModel(_model);
                }
            }
        }

        #endregion

        #region Debug

		[ContextMenu("Server Crash")]
		public void SimulateServerCrash()
        {
            // simulate by killing the low-level server socket
            Server?.ServerRPC?.Server?.KillServer();
        }

        #endregion

        #region Trial

        /*
        [ContextMenu("TRIAL/Test Text")]
        public void _test_trial_text() => TrialSettings._test_trial_text();
        
        [ContextMenu("TRIAL/Test Image")]
        public void _test_trial_image() => TrialSettings._test_trial_image();
        */

        [ContextMenu("TRIAL/Show text in MessageZone")]
        public void _test_messagezone_text() => TrialSettings._test_messagezone_text();

        #endregion

        #region Overlay

        private Text overlay_message;
        private Coroutine overlay_message_colorCR;
        private float overlay_message_time_end = 0;

        private void CreateStandardOverlayMessage()
        {
			GameObject textGameObject = new GameObject("_overlay_message");

			overlay_message = textGameObject.AddComponent<Text>();

			// Set the text properties
			overlay_message.text = "";
			overlay_message.color = Color.white;
			overlay_message.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
            overlay_message.fontStyle = FontStyle.Bold;
			overlay_message.fontSize = 60;
			overlay_message.alignment = TextAnchor.LowerCenter;
            overlay_message.horizontalOverflow = HorizontalWrapMode.Wrap;
            overlay_message.verticalOverflow = VerticalWrapMode.Overflow;
            overlay_message.raycastTarget = false;

			// Set the parent to the canvas
			textGameObject.transform.SetParent(Overlay.transform);

			// Get the RectTransform component
			RectTransform rectTransform = textGameObject.GetComponent<RectTransform>();

			// Set the RectTransform properties
			rectTransform.anchorMin = new Vector2(0, 0);
			rectTransform.anchorMax = new Vector2(1, 1);
			rectTransform.offsetMin = new Vector2(0, 0);
			rectTransform.offsetMax = new Vector2(0, 0);
			rectTransform.anchoredPosition = Vector2.zero;
            rectTransform.localScale = Vector3.one;

            overlay_message.gameObject.SetActive(false);
		}

        public void SetStandardMessage(string message, float duration=float.MaxValue)
        {
			IEnumerator _color_animation(float duration)
			{
				float halfDuration = duration / 2f;

				while (true)
				{
					for (float t = 0; t < halfDuration; t += Time.deltaTime)
					{
						float normalizedTime = t / halfDuration;
						overlay_message.color = Color.Lerp(Color.white, Color.black, normalizedTime);
						yield return null;
					}

					// Make sure the color is set to black
					overlay_message.color = Color.black;

					// Interpolate from black to white
					for (float t = 0; t < halfDuration; t += Time.deltaTime)
					{
						float normalizedTime = t / halfDuration;
						overlay_message.color = Color.Lerp(Color.black, Color.white, normalizedTime);
						yield return null;
					}

					// Make sure the color is set to white
					overlay_message.color = Color.white;
				}
			}

            if (overlay_message == null)
            {
                // still not created
                return;
            }

			if (string.IsNullOrEmpty(message))
            {
                if (overlay_message_colorCR != null)
                    StopCoroutine(overlay_message_colorCR);

                overlay_message_colorCR = null;
                overlay_message.text = "";
				overlay_message.color = Color.white;
				overlay_message.gameObject.SetActive(false);
                return;
            }

            overlay_message.text = message;
			overlay_message.color = Color.white;
			overlay_message.gameObject.SetActive(true);
            overlay_message_time_end = Time.time + duration;

            // interpolate to make it always visible
            if (overlay_message_colorCR == null)
                overlay_message_colorCR = StartCoroutine(_color_animation(1));
		}

        [ContextMenu("Standard Message (10s)")]
        public void _test_standardmessage1()
        {
            SetStandardMessage("PLEASE FOCUS", 10);
        }

		[ContextMenu("Standard Message (inf)")]
		public void _test_standardmessage2()
		{
			SetStandardMessage("INFINITE");
		}

		[ContextMenu("Standard Message (close)")]
		public void _test_standardmessage3()
		{
			SetStandardMessage(null);
		}

		#endregion

		#region JSON

		private static JsonSerializerSettings json_settings => new JsonSerializerSettings()
        {
            Formatting = Newtonsoft.Json.Formatting.Indented,
            TypeNameHandling = TypeNameHandling.Auto,
            NullValueHandling = NullValueHandling.Ignore,
            SerializationBinder = new IgnoreAssemblySerializationBinder()
        };

        public static void SerializeToJson(object obj, string filename)
        {
            string json = JsonConvert.SerializeObject(obj);

            json = SharedObjectMap.SharedObjectMap.CleanJsonFromGuids(json);

            File.WriteAllText(filename, json);
        }

        public static T DeserializeFromJson<T>(string filename)
        {
            return JsonConvert.DeserializeObject<T>(File.ReadAllText(filename));
        }

		#endregion

		#region Camera

        // standard s-argame games have camera not rotated
		public static bool IsCameraRotated 
            => Instance.Camera.transform.rotation.x != 0 || Instance.Camera.transform.rotation.y != 0 || Instance.Camera.transform.rotation.z != 0;

        public static UnityEngine.Vector3 ScreenToWorldPoint(Tabula.PMCore.Vector2f v2, float? keep_z=null)
        {
            if (!IsCameraRotated)
            {
                // standard orthogonal without rotation, we are parallel to the XY plane
                var pos = Instance.Camera.ScreenToWorldPoint(new UnityEngine.Vector3() { x = v2.x, y = Instance.Camera.pixelHeight - v2.y });
                pos.z = (keep_z == null ? 0 : (float)keep_z);
                return pos;
            }
            else
            {
                // camera is rotated, screentoworldpoint doesn't work, we need to intercept the plane mathematically
				Ray ray = Instance.Camera.ScreenPointToRay(new Vector2(v2.x, Instance.Camera.pixelHeight - v2.y));

                // Plane XY 
                Plane plane = new Plane(Vector3.forward, new Vector3(0, 0, (keep_z == null ? 0 : (float) keep_z)));

				// Calculate the intersection point of the ray with the plane
				if (plane.Raycast(ray, out float distance))
				{
					var pos = ray.GetPoint(distance);

                    // TODO: to keep the Z we should go in the direction of the camera

                    return pos;
				}
                else
                {
                    // error.. panes are parallel ??!?
                    return Vector3.zero;
                }
			}
        }

        public static Tabula.PMCore.Vector2f WorldToScreenPoint(UnityEngine.Vector3 pos)
        {
            // TODO: works with rotated camera?

            var spos = Instance.Camera.WorldToScreenPoint(pos);

            Vector2f v = new Vector2f()
            {
                x = spos.x,
                y = Instance.Camera.pixelHeight - spos.y
            };

            return v;
        }

        // Normalizes coordinates from/to Unity screen to TopLeft 0,0
        public static Vector3 FromToUnityScreen(Vector2f pos, float z=0)
        {
            return new Vector3(pos.x, Instance.Camera.pixelHeight - pos.y, z);
        }

        public static Vector3 FromToUnityScreen(Vector2i pos, float z=0)
        {
            return new Vector3(pos.x, Instance.Camera.pixelHeight - pos.y, z);
        }

		public static float PixelsToWorldUnits(int pixels)
		{
			// The orthographic size is half the vertical size of the viewing volume.
			float verticalWorldSize = Instance.Camera.orthographicSize * 2.0f;

			// Screen.height gives the screen height in pixels.
			float screenHeightInPixels = Instance.Camera.pixelHeight;

			// Calculate the size of a pixel in world units.
			float worldUnitsPerPixel = verticalWorldSize / screenHeightInPixels;

			// Convert pixel tolerance to world units.
			return pixels * worldUnitsPerPixel;
		}

		#endregion
	}

	public class IgnoreAssemblySerializationBinder : DefaultSerializationBinder
    {
        public override Type BindToType(string assemblyName, string typeName)
        {
            return Type.GetType(typeName);
        }

        public override void BindToName(Type serializedType, out string assemblyName, out string typeName)
        {
            assemblyName = null;
            typeName = serializedType.FullName;
        }
    }

    // Trial Settings  (not serializable, private)
    internal class TrialSettings
    {
		private float    font_size = 90;
		private float    text_z = -8;

		private float    trial_time = 3;
		private int      logo_sprite_width = 300;   // calculated depending on camera pixel size

		private int      screen_rows = 4;
		private int      screen_columns = 6;

		private string      logo_encoded = "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";

		private Canvas      canvas;
        private Texture     logo_texture;
		private Transform   message_zone;

        private bool        is_initialized = false;
        private bool        is_missing_features = false;    // true if license is present, but lacks some features

        #region Trial

		private void Initialize()
        {
            if (is_initialized)
                return;

			// Find PMCore canvas
			try
			{
				canvas = GameObject.Find("PMCore").transform.Find("overlay").GetComponent<Canvas>();
				if (canvas == null)
					throw new System.Exception();
			}
			catch
			{
				// no canvas found
				Debug.LogError("PMCoreInt: no overlay found");
			}

			// Find MessageZone
			try
			{
				// gets the root of the internal camera so we are in the right root (not DontDestroyOnLoad)
				//var t = GetRootTransform(PMCore_Server.Instance.Camera.transform);
				var t = (from e in UnityUtilities.GetTopLevelEntities() where e.name == "Entities" select e).FirstOrDefault()?.transform;
				if (t != null)
				{
					message_zone = UnityUtilities.FindInChildren(t, "MessageZone");
					if (message_zone == null)
						throw new Exception();
				}
				else
					throw new Exception();
			}
			catch
			{
				// no MessageZone found
				Debug.LogWarning("PMCoreInt: no MessageZone found");
			}

			// Load encoded logo
			try
			{
				logo_texture = DecodeStringToTexture(logo_encoded);
			}
			catch
			{
				// no canvas found
				Debug.LogError("PMCoreInt: cannot decode logo");
			}

            var license = LocalApp_Scriptable.Instance.LicenseInfo;

            is_missing_features = false;
            if (LocalApp_Scriptable.Instance.LicenseFeatures!=null)
			    foreach (var f in LocalApp_Scriptable.Instance.LicenseFeatures)
			    {
				    if (!LocalAppConfig.Instance.CheckLicenseFeature(f, true))
				    {
					    is_missing_features = true;
                        break;
				    }
			    }


			if (license.IsTrial() || is_missing_features)
			{
				// Setup nag times and frequencies

                if (is_missing_features)
                {
					// A license is present, but lacks features, so we are not in a trial, let's setup a fair-enough nag session

					trial_max_session_time = 25 * 60;   // 25 mins
					trial_nag_duration = 5;             // 5 secs
					trial_nag_count = 10;               // 10 nags
				}
				else if (license.IsTrialExpired())
				{
                    //  (trial expired)
                    trial_max_session_time = 5 * 60;    // 5 mins
                    trial_nag_duration = 10;            // 5 secs
					trial_nag_count = 10;               // 10 nags
				}
				else
				{
                    // WORKAROUND: it seems trial on Mac do not work... they give 0 as current day?
                    double trial_percentage = 0;

					if (license.getTrialDay() == 0)
                    {
                        // this will never change, let's handle it as a 0.5
                        trial_percentage = 0.5;
                    }
                    else
                        trial_percentage = license.getTrialPercentage();

					if (trial_percentage < 0.3)
					{
						trial_max_session_time = 25 * 60;   // 25 mins
						trial_nag_duration = 3;             // 3 secs
						trial_nag_count = 8;                // 8 nags
					}
					else if (trial_percentage < 0.6)
					{
						trial_max_session_time = 15 * 60;   // 15 mins
						trial_nag_duration = 5;             // 5 secs
						trial_nag_count = 8;                // 8 nags
					}
					else
					{
						trial_max_session_time = 10 * 60;   // 10 mins
						trial_nag_duration = 6;             // 6 secs
						trial_nag_count = 8;                // 8 nags
					}
				}
			}

            // give priority to logo size and compute columns and rows
            screen_columns = (int) ((float)PMCore_Server.Instance.Camera.pixelWidth / ((float)logo_sprite_width * 0.8f));
			screen_rows = (int)((float)PMCore_Server.Instance.Camera.pixelHeight / ((float)logo_sprite_width * 0.8f));

            // Trial rendering using camera overlay, otherwise draw OnGUI
            if (!PMCore_Server.Instance.DisablePM)
            {
                var cam = PMCore_Server.Instance.Camera;

                var shader = PMCore_Server.Instance.shaders!=null && PMCore_Server.Instance.shaders.Count>0 ? PMCore_Server.Instance.shaders[0] : null; // was  Shader.Find("S-ARGAME/overlay_tr");
                if (shader != null)
                {
                    trial_material = new Material(shader);

                    var cells = GetScreenGrid(cam.pixelWidth, cam.pixelHeight, screen_rows, screen_columns);

                    // Make sure main camera has the CameraOverlay component added for trial rendering, we will enable/disable it at will
                    camera_ext = cam.GetComponent<CameraOverlay>();
                    if (camera_ext == null)
                        camera_ext = cam.gameObject.AddComponent<CameraOverlay>();

                    camera_ext.Setup(trial_material, logo_texture, new Vector2(logo_sprite_width, logo_sprite_width), cells);

                    trial_ongui_rendering = false;
                }
                else
                {
                    // revert to direct rendering
                    trial_ongui_rendering = true;

                    Debug.LogError("No S-ARGAME/overlay_tr shader");
                }
            }
            else
                trial_ongui_rendering = true;

			is_initialized = true;
		}

		private float   trial_max_session_time = 20 * 60;   // max session time (will be reduced last trial days)
		private float   trial_nag_duration = 5;             // duration of the nag screen
        private int     trial_nag_count = 10;               // times nag will be shown during the session  

		private bool    trial_nag_screen = false;
		private float   trial_next_nag_time = -1;           // time when the next nag will be shown
        private float   trial_nag_screen_time_end = -1;     // time when the trial nag will end
		private int     trial_nag_index = -1;               // current nag in trial_nag_count
        private float   trial_timescale = 1f;             // time scaling when trial is enabled

        private bool    trial_ongui_rendering = false;      // true if we are in DisablePM or special cases

        private CameraOverlay camera_ext;                       // ref to the camera overlay component
        private Material  trial_material;

		internal void TrialLogic()
		{
            void _nag_enable()
            {
                // set trial_nag_screen to true, the TrialDraw() called from OnGUI() will draw immediate sprites.

				trial_nag_screen = true;
				trial_nag_screen_time_end = Time.time + trial_nag_duration;

				// activate timescale
				Time.timeScale = trial_timescale;

                if (camera_ext)
                {
                    camera_ext.enabled = true;
                    camera_ext.mode = 1;
                }
			}

            void _nag_disable()
            {
				// restore normal timescale
				trial_nag_screen = false;
				Time.timeScale = 1f;

				// move to next trial moment index if available, or stick to the last
				if ((trial_nag_index + 1) < trial_nag_count)
					trial_nag_index++;

				trial_next_nag_time = (trial_max_session_time / (float)trial_nag_count) * (float)trial_nag_index;

                if (camera_ext)
                {
                    //camera_ext.enabled = false;
                    camera_ext.enabled = true;
                    camera_ext.mode = 0;
                    camera_ext.mode_index = (camera_ext.mode_index + 1) % 4;    // corner
				}
			}

			// initialize references and defines different nag schemes depending on trial day
			Initialize();

            // If no features are missing, the camera overlay should be disabled
            if (!is_missing_features)
            {
                if (camera_ext!=null)
                    camera_ext.enabled = false;
 
                return;
            }

			// nag screen initialization
			if (trial_nag_index == -1)
			{
				trial_nag_index = 0;

                _nag_enable();
			}

			if (Time.time < trial_max_session_time)
			{
				// nag screens
				if (trial_nag_screen)
				{
                    // trial nag is shown, check when it ends (images will auto-destroy after duration has passed)

                    if (Time.time > trial_nag_screen_time_end)
                    {
                        _nag_disable();
					}
				}
				else
				{
					// check when activating next nag
					if (Time.time > trial_next_nag_time)
					{
                        _nag_enable();
					}
				}
			}
			else
			{
				// nag is always shown
				_nag_enable();
			}
		}
        
        // To be called in LateUpdate
        internal void TrialDraw_ScreenDirect()
        {
            if (!trial_ongui_rendering)
                return;

            if (!trial_nag_screen || logo_texture == null)
                return;
           
			// On screen (called OnGUI)

			foreach (var spos in GetScreenGrid(PMCore_Server.Instance.Camera.pixelWidth, PMCore_Server.Instance.Camera.pixelHeight, screen_rows, screen_columns))
			{
				GUI.DrawTexture(new Rect(spos.x - ((float) logo_sprite_width/2f), spos.y - ((float)logo_sprite_width / 2f), logo_sprite_width, logo_sprite_width), logo_texture);
			}
		}        

		#endregion

		#region Tests		

		public void _test_messagezone_text()
		{
			ShowTextInMessageZone("Please FOCUS the Window", font_size, trial_time);
		}

        #endregion

        #region Helpers

		private Texture2D DecodeStringToTexture(string encodedString)
		{
			byte[] textureBytes = Convert.FromBase64String(encodedString);
			Texture2D texture = new Texture2D(2, 2); // Create a temporary texture
			texture.LoadImage(textureBytes, true); // Load the image data into the texture
			return texture;
		}

		private List<Vector2> GetScreenGrid(int screen_width, int screen_height, int rows, int columns)
		{
			// Calculate the size of each cell in viewport coordinates
			float cellWidth = screen_width / (float) columns;
			float cellHeight = screen_height / (float) rows;

			List<Vector2> cells = new List<Vector2>();

			for (int i = 0; i < columns; i++)
			{
				for (int j = 0; j < rows; j++)
				{
					float centerX = (i * cellWidth) + (cellWidth / 2f);
					float centerY = (j * cellHeight) + (cellHeight / 2f);

					cells.Add(new Vector2(centerX, centerY));
				}
			}

			return cells;
		}

		private void ShowTextInMessageZone(string text, float font_size, float time)
		{
			Vector2 spos;
			if (message_zone != null)
				spos = PMCore_Server.Instance.Camera.WorldToScreenPoint(message_zone.transform.position);
			else
				spos = PMCore_Server.Instance.Camera.ViewportToScreenPoint(new Vector3(0.5f, 0.5f));

			CreateTemporaryText(text, spos, font_size, time);
		}

		private void CreateTemporaryText(string text, Vector3 spos, float font_size, float time)
		{
			// Create a new GameObject
			GameObject textObject = new GameObject("_trialtext");
			textObject.transform.SetParent(canvas.transform);

			var tm = textObject.AddComponent<TextMeshProUGUI>();

			tm.SetText($"<b>{text}</b>");

			tm.fontSize = font_size;
			tm.alignment = TextAlignmentOptions.Center;
			tm.verticalAlignment = VerticalAlignmentOptions.Middle;
			tm.horizontalAlignment = HorizontalAlignmentOptions.Center;

			tm.ForceMeshUpdate();

			RectTransform rt = textObject.GetComponent<RectTransform>();
			rt.anchorMin = new Vector2(0, 0);
			rt.anchorMax = new Vector2(0, 0);
			rt.pivot = new Vector2(0.5f, 0.5f);
			rt.sizeDelta = new Vector2(tm.preferredWidth, tm.preferredHeight);
			rt.anchoredPosition = new Vector2(spos.x, spos.y);
			rt.position = new Vector3(rt.position.x, rt.position.y, text_z);
			rt.localScale = Vector3.one;

			GameObject.Destroy(textObject, time);
		}

		private void CreateTemporaryTrialImage(Sprite sprite, Vector3 spos, float time)
		{
			GameObject imobj = new GameObject("_trialimage");
			imobj.transform.SetParent(canvas.transform);

			var im = imobj.AddComponent<UnityEngine.UI.Image>();

			im.sprite = sprite;

			RectTransform rt = imobj.GetComponent<RectTransform>();
			rt.anchorMin = new Vector2(0, 0);
			rt.anchorMax = new Vector2(0, 0);
			rt.pivot = new Vector2(0.5f, 0.5f);
			rt.sizeDelta = new Vector2(logo_sprite_width, logo_sprite_width);
			rt.anchoredPosition = new Vector2(spos.x, spos.y);
			rt.position = new Vector3(rt.position.x, rt.position.y, text_z);
			rt.localScale = Vector3.one;

            // auto-destroy the trial image after some time, so we don't keep references
			GameObject.Destroy(imobj, time);
		}

        #endregion

        private void OnDestroy()
        {
            // Dispose P2P instance when the component is destroyed
            _p2pInstance?.Dispose();
            _p2pInstance = null;
        }
	}

	// Rendering settings
	// NOTE: Needs LimitFPS component
	[Serializable]
	public partial class RenderingSettings
	{
		public int          target_fps = -1;
		public int          vsync = 0;
		public bool         show_fps = false;
        public string       fps_color = "#FFFFFFFF";
		public bool         log_fps = false;
		public float        log_interval = 0;
		public bool         taskbar_hide = false; // ?

        public void Dump()
        {
			LocalAppConfig.Instance?.log($"RenderingSettings {nameof(target_fps)}={target_fps} {nameof(vsync)}={vsync} {nameof(show_fps)}={show_fps} {nameof(fps_color)}={fps_color} {nameof(log_fps)}={log_fps} {nameof(log_interval)}={log_interval} {nameof(taskbar_hide)}={taskbar_hide}");
		}

        public bool OverrideFromCommandLine()
        {
            bool overridden = false;

            if (OSUtilities.getCommandLineArgument("-" + nameof(target_fps), ref target_fps))
                { LocalAppConfig.Instance?.log($"override RenderingSettings {nameof(target_fps)}={target_fps}"); overridden = true; }
			if (OSUtilities.getCommandLineArgument("-" + nameof(vsync), ref vsync))
				{ LocalAppConfig.Instance?.log($"override RenderingSettings {nameof(vsync)}={vsync}"); overridden = true; }
			if (OSUtilities.getCommandLineArgument("-" + nameof(show_fps), ref show_fps))
				{ LocalAppConfig.Instance?.log($"override RenderingSettings {nameof(show_fps)}={show_fps}"); overridden = true; }
			if (OSUtilities.getCommandLineArgument("-" + nameof(fps_color), ref fps_color))
				{ LocalAppConfig.Instance?.log($"override RenderingSettings {nameof(fps_color)}={fps_color}"); overridden = true; }
			if (OSUtilities.getCommandLineArgument("-" + nameof(log_fps), ref log_fps))
				{ LocalAppConfig.Instance?.log($"override RenderingSettings {nameof(log_fps)}={log_fps}"); overridden = true; }
			if (OSUtilities.getCommandLineArgument("-" + nameof(log_interval), ref log_interval))
				{ LocalAppConfig.Instance?.log($"override RenderingSettings {nameof(log_interval)}={log_interval}"); overridden = true; }
			if (OSUtilities.getCommandLineArgument("-" + nameof(taskbar_hide), ref taskbar_hide))
				{ LocalAppConfig.Instance?.log($"override RenderingSettings {nameof(taskbar_hide)}={taskbar_hide}"); overridden = true; }

            return overridden;
        }

        public bool Get(GameObject obj)
        {
			var limit = obj.GetComponent<LimitFPS>();
			if (limit == null)
				return false;

            target_fps = limit.TargetFPS;
            vsync = limit.VSyncCount;
            show_fps = limit.ShowFPS;
            fps_color = "#" + ColorUtility.ToHtmlStringRGBA(limit.ShowFPSColor);
            log_fps = limit.LogFPS;
            log_interval = limit.LogInterval;

            return true;
		}
        
        public bool Apply(GameObject obj)
        {
			// Apply Rendering Settings
			var limit = obj.GetComponent<LimitFPS>();
            if (limit == null)
                return false;

			limit.TargetFPS = target_fps;
			limit.VSyncCount = vsync;
			limit.ShowFPS = show_fps;
            if (ColorUtility.TryParseHtmlString(fps_color, out Color col))
                limit.ShowFPSColor = col;
                
			limit.LogFPS = log_fps;
			limit.LogInterval = log_interval;

            return true;
		}

	}
}

namespace Tabula.PMCore
{
    // Extend field wrapper with methods for dynamic calls

    public partial class IntegerFieldWrapper : WrapperBase<IntegerField>
    {
		// need this for presets
		public void SetValue(object value) => this.value = (int)Convert.ChangeType(value, typeof(int));
	}

	public partial class FloatFieldWrapper : WrapperBase<FloatField>
	{
		// need this for presets
		public void SetValue(object value) => this.value = (float)Convert.ChangeType(value, typeof(float));
	}

	public partial class BoolFieldWrapper : WrapperBase<BoolField>
	{
		// need this for presets
		public void SetValue(object value) => this.value = (bool)Convert.ChangeType(value, typeof(bool));
	}

	public partial class StringFieldWrapper : WrapperBase<StringField>
	{
		// need this for presets
		public void SetValue(object value) => this.value = (string)Convert.ChangeType(value, typeof(string));
	}

	public partial class ChoiceFieldWrapper : WrapperBase<ChoiceField>
	{
		// need this for presets
		public void SetValue(object value) => this.value = (string)Convert.ChangeType(value, typeof(string));
	}

	public partial class SliderFloatFieldWrapper : WrapperBase<SliderFloatField>
	{
		// need this for presets
		public void SetValue(object value) => this.value = (float)Convert.ChangeType(value, typeof(float));
	}
}

